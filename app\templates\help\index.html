{% extends "base.html" %}

{% block title %}帮助中心{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-4">
                        <i class="fas fa-life-ring"></i> 帮助中心
                    </h1>
                    <p class="lead mb-4">校园餐智慧食堂平台使用指南与技术支持</p>
                    
                    <!-- 搜索框 -->
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control form-control-lg" id="helpSearch" 
                                       placeholder="🔍 搜索帮助内容..." onkeyup="searchHelp()">
                                <button class="btn btn-light" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速访问 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5><i class="fas fa-rocket"></i> 快速访问</h5>
                    <div class="mt-3">
                        <a href="{{ url_for('help.financial_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-calculator"></i> 财务管理
                        </a>
                        <a href="{{ url_for('help.daily_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-calendar-day"></i> 日常管理
                        </a>
                        <a href="{{ url_for('help.supply_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-truck"></i> 供应链管理
                        </a>
                        <a href="{{ url_for('help.system_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-cogs"></i> 系统设置
                        </a>
                        <a href="{{ url_for('help.troubleshooting') }}" class="btn btn-outline-light mb-2">
                            <i class="fas fa-exclamation-triangle"></i> 故障排除
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 财务管理帮助 -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-calculator"></i> 财务管理</h5>
                </div>
                <div class="card-body">
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-chart-line text-primary"></i> 会计科目管理</h6>
                        <p class="text-muted mb-2">管理系统会计科目和学校自定义科目</p>
                        <div class="btn-group btn-group-sm">
                            <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-outline-primary">
                                <i class="fas fa-book"></i> 详细指南
                            </a>
                            <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-outline-success">
                                <i class="fas fa-external-link-alt"></i> 打开功能
                            </a>
                        </div>
                    </div>
                    
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-file-invoice text-success"></i> 财务凭证</h6>
                        <p class="text-muted mb-2">创建、编辑和管理财务凭证</p>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-external-link-alt"></i> 凭证管理
                        </a>
                    </div>
                    
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-book text-info"></i> 财务报表</h6>
                        <p class="text-muted mb-2">查看资产负债表、利润表等财务报表</p>
                        <a href="{{ url_for('financial.reports_index') }}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-external-link-alt"></i> 财务报表
                        </a>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('help.financial_help') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> 查看更多财务帮助
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日常管理帮助 -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-calendar-day"></i> 日常管理</h5>
                </div>
                <div class="card-body">
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-utensils text-primary"></i> 每日菜单</h6>
                        <p class="text-muted mb-2">管理每日菜单和营养搭配</p>
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> 菜单管理
                        </a>
                    </div>
                    
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-search text-success"></i> 食品检查</h6>
                        <p class="text-muted mb-2">食品安全检查和质量监控</p>
                        <a href="{{ url_for('inspection.index') }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-external-link-alt"></i> 检查管理
                        </a>
                    </div>
                    
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-camera text-info"></i> 留样管理</h6>
                        <p class="text-muted mb-2">食品留样记录和管理</p>
                        <a href="{{ url_for('food_sample.index') }}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-external-link-alt"></i> 留样记录
                        </a>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('help.daily_help') }}" class="btn btn-success">
                            <i class="fas fa-arrow-right"></i> 查看更多日常管理帮助
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 供应链管理帮助 -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-truck"></i> 供应链管理</h5>
                </div>
                <div class="card-body">
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-shopping-cart text-primary"></i> 采购订单</h6>
                        <p class="text-muted mb-2">创建和管理采购订单</p>
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> 采购管理
                        </a>
                    </div>
                    
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-warehouse text-success"></i> 库存管理</h6>
                        <p class="text-muted mb-2">入库、出库和库存查询</p>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-external-link-alt"></i> 库存查询
                        </a>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('help.supply_help') }}" class="btn btn-info">
                            <i class="fas fa-arrow-right"></i> 查看更多供应链帮助
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 故障排除 -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-exclamation-triangle"></i> 故障排除</h5>
                </div>
                <div class="card-body">
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-chart-line text-danger"></i> 会计科目问题</h6>
                        <p class="text-muted mb-2">凭证明细无法添加、科目下拉框为空</p>
                        <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-wrench"></i> 解决方案
                        </a>
                    </div>
                    
                    <div class="help-item p-3 mb-3 border rounded">
                        <h6><i class="fas fa-sign-in-alt text-warning"></i> 登录权限问题</h6>
                        <p class="text-muted mb-2">无法登录或权限不足</p>
                        <a href="{{ url_for('help.troubleshooting') }}" class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-key"></i> 权限指南
                        </a>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('help.troubleshooting') }}" class="btn btn-danger">
                            <i class="fas fa-arrow-right"></i> 查看更多故障排除
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 联系支持 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h5><i class="fas fa-headset"></i> 需要更多帮助？</h5>
                    <p class="text-muted">如果您在使用过程中遇到其他问题，可以通过以下方式获取支持：</p>
                    <div class="row justify-content-center">
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <i class="fas fa-phone text-primary fa-2x mb-2"></i>
                                    <h6>技术支持热线</h6>
                                    <p class="text-muted">18373062333</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body">
                                    <i class="fas fa-envelope text-success fa-2x mb-2"></i>
                                    <h6>邮件支持</h6>
                                    <p class="text-muted"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <i class="fas fa-comments text-info fa-2x mb-2"></i>
                                    <h6>在线客服</h6>
                                    <p class="text-muted">工作日 9:00-18:00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function searchHelp() {
    const searchTerm = document.getElementById('helpSearch').value.toLowerCase();
    const helpItems = document.querySelectorAll('.help-item');
    
    helpItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            item.style.display = 'block';
            item.style.backgroundColor = searchTerm ? '#fff3cd' : '';
        } else {
            item.style.display = searchTerm ? 'none' : 'block';
            item.style.backgroundColor = '';
        }
    });
}
</script>
{% endblock %}
