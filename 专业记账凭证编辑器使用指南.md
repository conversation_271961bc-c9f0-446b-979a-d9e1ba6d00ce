# 专业记账凭证编辑器使用指南

## 概述

我们为您开发了一个专业的记账凭证编辑器，提供类似Excel的编辑体验，让财务人员能够更高效、更直观地处理记账凭证。

## 访问地址

**专业编辑器**: http://14.103.246.164/financial/vouchers/8/edit-pro

**对比原版编辑器**: http://14.103.246.164/financial/vouchers/8/edit

## 主要特性

### 🎯 类Excel编辑体验
- **键盘导航**: Tab、Enter、方向键快速移动
- **单元格编辑**: 点击即可编辑，支持多行文本
- **智能焦点**: 自动跳转到下一个逻辑单元格
- **快捷操作**: 复制、粘贴、删除行等

### 🔍 智能科目选择
- **下拉搜索**: 输入关键词快速筛选科目
- **分类显示**: 按科目类型分组显示
- **自动补全**: 支持编码和名称搜索
- **系统科目**: 支持系统预设和学校自定义科目

### ⚡ 实时计算验证
- **借贷平衡**: 实时检查借贷是否平衡
- **金额格式化**: 自动格式化金额显示
- **大写转换**: 自动转换金额大写
- **数据验证**: 实时验证数据完整性

### 🚀 高效操作
- **自动保存**: 编辑后自动保存到服务器
- **批量操作**: 支持复制粘贴整行数据
- **快捷键**: 丰富的快捷键支持
- **状态提示**: 实时显示操作状态

## 快捷键说明

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Tab` | 下一个单元格 | 在同一行内移动到下一个可编辑单元格 |
| `Shift+Tab` | 上一个单元格 | 在同一行内移动到上一个可编辑单元格 |
| `Enter` | 下一行 | 移动到下一行的相同列 |
| `↑↓` | 上下移动 | 在同一列内上下移动 |
| `Ctrl+N` | 添加新行 | 在表格末尾添加新的分录行 |
| `Del` | 删除行 | 删除当前选中的行 |
| `Ctrl+C` | 复制行 | 复制当前行到剪贴板 |
| `Ctrl+V` | 粘贴行 | 粘贴剪贴板中的行 |
| `Ctrl+S` | 保存凭证 | 保存整个凭证 |
| `F9` | 检查平衡 | 检查借贷是否平衡 |
| `F1` | 显示/隐藏帮助 | 切换快捷键帮助显示 |

## 操作流程

### 1. 编辑凭证基本信息
1. 在页面顶部修改凭证类型、日期、附件数量
2. 修改后自动保存到服务器

### 2. 编辑凭证明细
1. **添加新行**: 点击"添加新行"按钮或使用`Ctrl+N`
2. **选择科目**: 点击科目单元格，在下拉框中搜索选择
3. **输入摘要**: 在摘要单元格输入分录说明
4. **输入金额**: 在借方或贷方单元格输入金额（不能同时输入）
5. **自动保存**: 完整填写后自动保存到服务器

### 3. 科目选择技巧
- **快速搜索**: 直接输入科目编码或名称关键词
- **分类浏览**: 按资产、负债、净资产、收入、费用分类查看
- **系统科目**: 支持选择系统预设科目和学校自定义科目

### 4. 数据验证
- **借贷平衡**: 系统实时检查借贷是否平衡
- **金额验证**: 确保金额格式正确
- **必填检查**: 确保摘要和科目已填写

### 5. 保存和提交
1. **自动保存**: 编辑过程中自动保存明细
2. **手动保存**: 使用`Ctrl+S`或点击保存按钮
3. **检查平衡**: 使用`F9`或点击检查平衡按钮
4. **提交审核**: 确认无误后提交审核

## 界面说明

### 工具栏功能
- **添加行**: 在表格末尾添加新的分录行
- **删除行**: 删除当前选中的行
- **复制行**: 复制当前行到剪贴板
- **粘贴行**: 粘贴剪贴板中的行
- **保存**: 保存整个凭证
- **检查平衡**: 验证借贷是否平衡

### 状态指示器
- **借贷平衡**: 绿色显示"借贷平衡"
- **借贷不平衡**: 红色显示"不平衡(差额: X.XX)"
- **未检查**: 灰色显示"未检查"

### 表格结构
- **行号**: 自动编号，不可编辑
- **摘要**: 分录说明，支持多行文本
- **会计科目**: 点击选择科目，支持搜索
- **借方金额**: 借方金额，与贷方互斥
- **贷方金额**: 贷方金额，与借方互斥
- **操作**: 删除行按钮

## 优势对比

### 专业编辑器 vs 原版编辑器

| 特性 | 专业编辑器 | 原版编辑器 |
|------|------------|------------|
| 键盘导航 | ✅ 完整支持 | ❌ 基础支持 |
| 快捷键 | ✅ 丰富快捷键 | ❌ 有限快捷键 |
| 科目选择 | ✅ 智能下拉搜索 | ✅ 弹窗选择 |
| 自动保存 | ✅ 实时自动保存 | ❌ 手动保存 |
| 复制粘贴 | ✅ 支持行复制粘贴 | ❌ 不支持 |
| 用户体验 | ✅ 类Excel体验 | ❌ 传统表单 |
| 操作效率 | ✅ 高效快速 | ❌ 相对较慢 |
| 学习成本 | ✅ 符合习惯 | ❌ 需要适应 |

## 技术特性

### 前端技术
- **原生JavaScript**: 无依赖，性能优异
- **CSS Grid/Flexbox**: 响应式布局
- **事件委托**: 高效的事件处理
- **防抖处理**: 优化自动保存性能

### 后端支持
- **RESTful API**: 标准的API接口
- **实时验证**: 服务端数据验证
- **事务处理**: 确保数据一致性
- **权限控制**: 完整的权限验证

### 数据安全
- **自动备份**: 编辑过程自动备份
- **版本控制**: 支持数据版本管理
- **权限隔离**: 学校数据严格隔离
- **审计日志**: 完整的操作日志

## 常见问题

### Q1: 如何快速输入相同摘要？
**A**: 使用复制粘贴功能（Ctrl+C / Ctrl+V）复制整行，然后修改科目和金额。

### Q2: 科目选择器没有显示怎么办？
**A**: 确保网络连接正常，刷新页面重试。如果问题持续，请检查浏览器控制台错误信息。

### Q3: 自动保存失败怎么办？
**A**: 检查网络连接，确保数据完整（摘要、科目、金额都已填写），手动点击保存按钮。

### Q4: 借贷不平衡如何处理？
**A**: 使用F9检查具体差额，检查每行的借贷金额是否正确，确保借方总额等于贷方总额。

### Q5: 快捷键不生效怎么办？
**A**: 确保当前焦点在表格内，某些浏览器扩展可能会干扰快捷键，可以尝试禁用扩展。

## 浏览器兼容性

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ❌ IE 11及以下

## 性能优化

- **懒加载**: 科目数据按需加载
- **虚拟滚动**: 大量数据时使用虚拟滚动
- **防抖节流**: 优化输入和保存性能
- **缓存机制**: 科目数据客户端缓存

现在您可以体验更专业、更高效的记账凭证编辑器了！
