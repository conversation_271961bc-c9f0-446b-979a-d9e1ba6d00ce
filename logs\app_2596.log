2025-06-08 20:22:34,293 INFO: 应用启动 - PID: 2596 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:823]
2025-06-08 20:23:12,883 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-08 20:23:12,897 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-08 20:28:06,836 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-08 20:28:06,849 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-08 20:32:22,269 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-08 20:32:22,279 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-08 20:35:30,833 INFO: 使用区域 7 (岳阳县第一中学) 作为系统区域 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:635]
2025-06-08 20:35:30,837 INFO: 删除了 147 个有问题的科目 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:644]
2025-06-08 20:35:43,001 ERROR: 初始化系统会计科目失败: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]INSERT 语句与 FOREIGN KEY 约束"FK_accounting_subjects_area"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.administrative_areas", column \'id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: 
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (?, ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('1001', '库存现金', None, 1, '资产', '借方', 1, True, True, '系统标准会计科目 - 库存现金', 34)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:389]
2025-06-08 20:36:28,783 ERROR: 强制初始化系统会计科目失败: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]INSERT 语句与 FOREIGN KEY 约束"FK_accounting_subjects_area"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.administrative_areas", column \'id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: 
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (?, ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('1001', '库存现金', None, 1, '资产', '借方', 1, True, True, '系统标准会计科目 - 库存现金', 34)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:559]
