{% extends 'base.html' %}

{% block title %}会计科目文本树 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .text-tree-container {
    margin-top: 20px;
  }
  .text-display {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.6;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 20px;
    white-space: pre-wrap;
    overflow-x: auto;
    min-height: 500px;
  }
  .text-display.dark-theme {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  .text-controls {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
  }
  .stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 20px;
    margin-bottom: 20px;
  }
  .stats-card h5 {
    margin-bottom: 10px;
    font-weight: 600;
  }
  .stats-card .stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .search-highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 3px;
  }
  @media print {
    .no-print {
      display: none !important;
    }
    .text-display {
      border: none;
      background: white !important;
      color: black !important;
      font-size: 12px;
      padding: 0;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- 面包屑导航 -->
  <nav aria-label="breadcrumb" class="no-print">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
      <li class="breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></li>
      <li class="breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目</a></li>
      <li class="breadcrumb-item active">文本树视图</li>
    </ol>
  </nav>

  <div class="row">
    <div class="col-md-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between no-print">
          <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-sitemap"></i> 会计科目文本树视图
          </h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
              <div class="dropdown-header">操作:</div>
              <a class="dropdown-item" href="#" onclick="copyToClipboard()"><i class="fas fa-copy fa-sm fa-fw mr-2 text-gray-400"></i>复制文本</a>
              <a class="dropdown-item" href="#" onclick="downloadText()"><i class="fas fa-download fa-sm fa-fw mr-2 text-gray-400"></i>下载文本</a>
              <a class="dropdown-item" href="#" onclick="printView()"><i class="fas fa-print fa-sm fa-fw mr-2 text-gray-400"></i>打印</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="{{ url_for('financial.accounting_subjects_index') }}"><i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>返回列表</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 统计信息 -->
          <div class="stats-card no-print">
            <div class="row">
              <div class="col-md-3">
                <h5><i class="fas fa-sitemap"></i> 科目统计</h5>
                <div class="stats-number">{{ subjects_count }}</div>
                <small>总科目数量</small>
              </div>
              <div class="col-md-3">
                <h5><i class="fas fa-layer-group"></i> 层级结构</h5>
                <div class="stats-number">6</div>
                <small>科目类型</small>
              </div>
              <div class="col-md-3">
                <h5><i class="fas fa-balance-scale"></i> 平衡检查</h5>
                <div class="stats-number text-success">✓</div>
                <small>结构完整</small>
              </div>
              <div class="col-md-3">
                <h5><i class="fas fa-clock"></i> 更新时间</h5>
                <div class="stats-number" style="font-size: 1.2rem;">{{ current_time.strftime('%H:%M') }}</div>
                <small>{{ current_time.strftime('%Y-%m-%d') }}</small>
              </div>
            </div>
          </div>

          <!-- 文本控制工具栏 -->
          <div class="text-controls no-print">
            <div class="row">
              <div class="col-md-8">
                <div class="form-group mb-0">
                  <div class="row">
                    <div class="col-md-3">
                      <label for="fontSize" class="form-label">字体大小:</label>
                      <select id="fontSize" class="form-control form-control-sm">
                        <option value="12">12px</option>
                        <option value="14" selected>14px</option>
                        <option value="16">16px</option>
                        <option value="18">18px</option>
                        <option value="20">20px</option>
                      </select>
                    </div>
                    
                    <div class="col-md-3">
                      <label class="form-label">主题:</label>
                      <div class="btn-group btn-group-sm w-100" role="group">
                        <button type="button" class="btn btn-outline-secondary active" id="lightTheme">浅色</button>
                        <button type="button" class="btn btn-outline-secondary" id="darkTheme">深色</button>
                      </div>
                    </div>
                    
                    <div class="col-md-6">
                      <label for="searchText" class="form-label">搜索科目:</label>
                      <div class="input-group">
                        <input type="text" class="form-control form-control-sm" id="searchText" placeholder="输入科目编码或名称...">
                        <div class="input-group-append">
                          <button class="btn btn-outline-secondary btn-sm" onclick="searchInText()">
                            <i class="fas fa-search"></i>
                          </button>
                          <button class="btn btn-outline-secondary btn-sm" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4 text-right">
                <label class="form-label d-block">操作:</label>
                <div class="btn-group" role="group">
                  <button class="btn btn-primary btn-sm" onclick="copyToClipboard()">
                    <i class="fas fa-copy"></i> 复制
                  </button>
                  <button class="btn btn-success btn-sm" onclick="downloadText()">
                    <i class="fas fa-download"></i> 下载
                  </button>
                  <button class="btn btn-info btn-sm" onclick="printView()">
                    <i class="fas fa-print"></i> 打印
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 文本内容显示 -->
          <div class="text-display" id="subjectsText">{{ subjects_text }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
  // 字体大小控制
  $('#fontSize').on('change', function() {
    const fontSize = $(this).val() + 'px';
    $('.text-display').css('font-size', fontSize);
  });
  
  // 主题切换
  $('#lightTheme').on('click', function() {
    $('.text-display').removeClass('dark-theme');
    $('#lightTheme').addClass('active');
    $('#darkTheme').removeClass('active');
  });
  
  $('#darkTheme').on('click', function() {
    $('.text-display').addClass('dark-theme');
    $('#darkTheme').addClass('active');
    $('#lightTheme').removeClass('active');
  });
  
  // 搜索功能
  $('#searchText').on('keyup', function(e) {
    if (e.key === 'Enter') {
      searchInText();
    }
  });
});

function copyToClipboard() {
  const text = $('#subjectsText').text();
  
  navigator.clipboard.writeText(text).then(function() {
    showAlert('科目树文本已复制到剪贴板', 'success');
  }).catch(function(err) {
    console.error('复制失败:', err);
    showAlert('复制失败，请手动选择文本复制', 'danger');
  });
}

function downloadText() {
  const text = $('#subjectsText').text();
  const filename = '会计科目树_' + new Date().toISOString().slice(0, 10) + '.txt';
  
  const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  showAlert('科目树文本文件下载完成', 'success');
}

function printView() {
  window.print();
}

function searchInText() {
  const searchTerm = $('#searchText').val().trim();
  const textDisplay = $('#subjectsText');
  
  if (!searchTerm) {
    clearSearch();
    return;
  }
  
  // 保存原始文本
  if (!textDisplay.attr('data-original')) {
    textDisplay.attr('data-original', textDisplay.text());
  }
  
  const originalText = textDisplay.attr('data-original');
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');
  
  textDisplay.html(highlightedText);
  
  const matchCount = (originalText.match(regex) || []).length;
  if (matchCount > 0) {
    showAlert(`找到 ${matchCount} 个匹配项`, 'info');
  } else {
    showAlert('未找到匹配项', 'warning');
  }
}

function clearSearch() {
  const textDisplay = $('#subjectsText');
  const originalText = textDisplay.attr('data-original');
  
  if (originalText) {
    textDisplay.html(originalText);
  }
  
  $('#searchText').val('');
}

function showAlert(message, type) {
  const alertClass = type === 'danger' ? 'alert-danger' : 
                    type === 'success' ? 'alert-success' : 
                    type === 'warning' ? 'alert-warning' : 'alert-info';
  
  const alert = `
    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
      ${message}
      <button type="button" class="close" data-dismiss="alert">
        <span>&times;</span>
      </button>
    </div>
  `;
  
  // 移除旧的提示
  $('.alert').remove();
  
  // 添加新提示
  $('.card-body').prepend(alert);
  
  // 3秒后自动移除
  setTimeout(function() {
    $('.alert').fadeOut();
  }, 3000);
}
</script>
{% endblock %}
