"""
财务凭证管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import FinancialVoucher, VoucherDetail, AccountingSubject
from app.forms.financial import FinancialVoucherForm
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text, func
from datetime import datetime, date
from decimal import Decimal
from app.utils.financial_tools import financial_calculator, report_generator, beancount_integration
from app.utils.financial_text_processor import FinancialTextProcessor, BeanCountIntegration


def get_school_pinyin_initials(school_name):
    """获取学校名称所有汉字的拼音首字母"""
    # 扩展的拼音首字母映射表
    pinyin_map = {
        # 常用地名
        '北': 'B', '京': 'J', '上': 'S', '海': 'H', '天': 'T', '津': 'J',
        '重': 'C', '庆': 'Q', '河': 'H', '山': 'S', '西': 'X', '东': 'D',
        '南': 'N', '湖': 'H', '广': 'G', '四': 'S', '川': 'C', '贵': 'G',
        '州': 'Z', '云': 'Y', '陕': 'S', '甘': 'G', '肃': 'S', '青': 'Q',
        '夏': 'X', '新': 'X', '疆': 'J', '台': 'T', '湾': 'W', '港': 'G',
        '澳': 'A', '门': 'M', '市': 'S', '区': 'Q', '县': 'X', '镇': 'Z',
        '乡': 'X', '村': 'C', '内': 'N', '蒙': 'M', '古': 'G', '辽': 'L',
        '宁': 'N', '吉': 'J', '林': 'L', '黑': 'H', '龙': 'L', '江': 'J',
        '苏': 'S', '浙': 'Z', '安': 'A', '徽': 'H', '福': 'F', '建': 'J',
        '香': 'X',

        # 朝阳相关
        '朝': 'C', '阳': 'Y',

        # 学校相关
        '学': 'X', '校': 'X', '小': 'X', '中': 'Z', '大': 'D', '高': 'G',
        '第': 'D', '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W',
        '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
        '实': 'S', '验': 'Y', '民': 'M', '族': 'Z', '外': 'W', '国': 'G',
        '语': 'Y', '职': 'Z', '业': 'Y', '技': 'J', '术': 'S', '师': 'S',
        '范': 'F', '幼': 'Y', '儿': 'E', '园': 'Y', '附': 'F', '属': 'S',

        # 其他常用字
        '人': 'R', '文': 'W', '理': 'L', '工': 'G', '农': 'N', '医': 'Y',
        '师': 'S', '范': 'F', '艺': 'Y', '体': 'T', '音': 'Y', '美': 'M',
        '科': 'K', '信': 'X', '息': 'X', '电': 'D', '子': 'Z', '机': 'J',
        '械': 'X', '建': 'J', '筑': 'Z', '环': 'H', '境': 'J', '材': 'C',
        '料': 'L', '化': 'H', '生': 'S', '物': 'W', '数': 'S', '统': 'T',
        '计': 'J', '经': 'J', '济': 'J', '管': 'G', '营': 'Y', '财': 'C',
        '会': 'H', '法': 'F', '政': 'Z', '历': 'L', '史': 'S', '哲': 'Z',
        '思': 'S', '想': 'X', '马': 'M', '克': 'K', '列': 'L', '毛': 'M',
        '邓': 'D', '江': 'J', '胡': 'H', '习': 'X', '近': 'J', '平': 'P',

        # 补充城市名
        '深': 'S', '圳': 'Z', '浦': 'P', '东': 'D', '南': 'N', '山': 'S',
        '宝': 'B', '安': 'A', '龙': 'L', '岗': 'G', '福': 'F', '田': 'T',
        '罗': 'L', '湖': 'H', '盐': 'Y', '田': 'T', '坪': 'P', '山': 'S',
        '光': 'G', '明': 'M', '大': 'D', '鹏': 'P', '龙': 'L', '华': 'H'
    }

    # 提取所有汉字的拼音首字母
    initials = ''
    for char in school_name:
        if char in pinyin_map:
            initials += pinyin_map[char]
        elif char.isalpha():
            initials += char.upper()
        # 跳过数字、标点符号等其他字符

    # 不限制长度，保留所有汉字的拼音首字母以确保唯一性
    # 例如：朝阳区第一小学 -> CYQDYXX
    # 例如：朝阳区实验中学 -> CYQSYZX
    return initials


def generate_voucher_number(user_area, voucher_date=None):
    """生成凭证号"""
    if voucher_date is None:
        voucher_date = date.today()

    # 获取学校名称的拼音首字母
    school_initials = get_school_pinyin_initials(user_area.name)

    # 生成凭证号前缀：学校拼音首字母 + PZ + 日期
    voucher_prefix = f"{school_initials}PZ{voucher_date.strftime('%Y%m%d')}"

    # 查找当日最大凭证号
    last_voucher = FinancialVoucher.query.filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_number.like(f'{voucher_prefix}%')
    ).order_by(FinancialVoucher.voucher_number.desc()).first()

    if last_voucher:
        last_number = int(last_voucher.voucher_number[-3:])
        voucher_number = f"{voucher_prefix}{last_number + 1:03d}"
    else:
        voucher_number = f"{voucher_prefix}001"

    return voucher_number


@financial_bp.route('/vouchers')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def vouchers_index(user_area):
    """财务凭证列表"""
    
    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    voucher_type = request.args.get('voucher_type', '').strip()
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    
    # 构建查询
    query = FinancialVoucher.query.filter_by(area_id=user_area.id)
    
    if keyword:
        query = query.filter(
            db.or_(
                FinancialVoucher.voucher_number.like(f'%{keyword}%'),
                FinancialVoucher.summary.like(f'%{keyword}%')
            )
        )
    
    if voucher_type:
        query = query.filter_by(voucher_type=voucher_type)
    
    if status:
        query = query.filter_by(status=status)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(FinancialVoucher.voucher_date >= start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(FinancialVoucher.voucher_date <= end_date_obj)
        except ValueError:
            pass
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    vouchers = query.order_by(FinancialVoucher.voucher_date.desc(), 
                             FinancialVoucher.voucher_number.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('financial/vouchers/index.html',
                         vouchers=vouchers,
                         keyword=keyword,
                         voucher_type=voucher_type,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/vouchers/create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def create_voucher(user_area):
    """创建财务凭证"""
    form = FinancialVoucherForm()
    
    if form.validate_on_submit():
        try:
            # 生成凭证号（使用新的生成函数）
            voucher_number = generate_voucher_number(user_area, date.today())

            # 使用字符串格式化避免参数绑定问题
            def safe_sql_string(value):
                if value is None:
                    return 'NULL'
                # 避免在 f-string 中使用反斜杠
                escaped_value = str(value).replace("'", "''")
                return f"'{escaped_value}'"

            # 确保日期类型正确
            voucher_date = form.voucher_date.data
            if isinstance(voucher_date, datetime):
                voucher_date = voucher_date.date()

            # 使用字符串格式化的SQL
            insert_sql = text(f"""
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                ({safe_sql_string(voucher_number)},
                 '{voucher_date}',
                 {user_area.id},
                 {safe_sql_string(form.voucher_type.data)},
                 {safe_sql_string(form.summary.data)},
                 0.00,
                 {safe_sql_string('草稿')},
                 {safe_sql_string('手工录入')},
                 NULL,
                 0,
                 {current_user.id},
                 NULL,
                 NULL,
                 NULL,
                 NULL,
                 {safe_sql_string(form.notes.data)})
            """)

            # 执行插入
            db.session.execute(insert_sql)

            # 获取刚插入的记录ID
            select_sql = text("""
                SELECT id FROM financial_vouchers
                WHERE voucher_number = :voucher_number AND area_id = :area_id
            """)
            result = db.session.execute(select_sql, {
                'voucher_number': voucher_number,
                'area_id': user_area.id
            })
            voucher_id = result.fetchone()[0]
            db.session.commit()
            
            flash('财务凭证创建成功', 'success')
            return redirect(url_for('financial.edit_voucher', id=voucher_id))
            
        except Exception as e:
            db.session.rollback()
            import traceback
            error_details = traceback.format_exc()
            current_app.logger.error(f"创建财务凭证失败: {str(e)}")
            current_app.logger.error(f"详细错误信息: {error_details}")
            flash(f'创建失败：{str(e)}', 'danger')
    
    return render_template('financial/vouchers/form.html', form=form)











@financial_bp.route('/vouchers/<int:id>')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def view_voucher(id, user_area):
    """查看财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()
    
    return render_template('financial/vouchers/view.html', 
                         voucher=voucher, details=details)


@financial_bp.route('/vouchers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def edit_voucher(id, user_area):
    """编辑财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id,
        area_id=user_area.id
    ).first_or_404()

    # 已审核的凭证不允许编辑
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许编辑', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    form = FinancialVoucherForm(obj=voucher)

    if form.validate_on_submit():
        try:
            # 使用 ORM 更新凭证
            voucher.voucher_date = form.voucher_date.data
            voucher.voucher_type = form.voucher_type.data
            voucher.summary = form.summary.data
            voucher.notes = form.notes.data

            db.session.commit()

            flash('财务凭证更新成功', 'success')
            return redirect(url_for('financial.view_voucher', id=id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新财务凭证失败: {str(e)}")
            flash('更新失败，请重试', 'danger')

    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()

    # 获取会计科目数据，直接在模板中渲染
    from app.models_financial import AccountingSubject
    from app.models import AdministrativeArea
    from sqlalchemy import or_, and_

    # 获取系统区域ID
    system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
    system_area_id = system_area.id if system_area else 0

    # 获取所有会计科目
    all_subjects = AccountingSubject.query.filter(
        or_(
            and_(
                AccountingSubject.area_id == system_area_id,
                AccountingSubject.is_system == True
            ),
            and_(
                AccountingSubject.area_id == user_area.id,
                AccountingSubject.is_system == False
            )
        ),
        AccountingSubject.is_active == True
    ).order_by(AccountingSubject.code).all()

    # 按类型和层级组织科目数据
    subjects_data = {}
    for subject in all_subjects:
        if subject.subject_type not in subjects_data:
            subjects_data[subject.subject_type] = {'level1': [], 'level2': {}}

        if subject.level == 1:
            subjects_data[subject.subject_type]['level1'].append({
                'id': subject.id,
                'code': subject.code,
                'name': subject.name,
                'display_name': f'{subject.code} - {subject.name}'
            })
        elif subject.level == 2 and subject.parent_id:
            if subject.parent_id not in subjects_data[subject.subject_type]['level2']:
                subjects_data[subject.subject_type]['level2'][subject.parent_id] = []
            subjects_data[subject.subject_type]['level2'][subject.parent_id].append({
                'id': subject.id,
                'code': subject.code,
                'name': subject.name,
                'display_name': f'{subject.code} - {subject.name}'
            })

    return render_template('financial/vouchers/edit.html',
                         form=form, voucher=voucher, details=details, subjects_data=subjects_data)





@financial_bp.route('/vouchers/<int:id>/delete', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'delete')
def delete_voucher(id, user_area):
    """删除财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 已审核的凭证不允许删除
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许删除', 'warning')
        return redirect(url_for('financial.vouchers_index'))
    
    try:
        # 使用 ORM 删除（级联删除明细）
        db.session.delete(voucher)
        db.session.commit()
        
        flash('财务凭证删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除财务凭证失败: {str(e)}")
        flash('删除失败，请重试', 'danger')
    
    return redirect(url_for('financial.vouchers_index'))


@financial_bp.route('/vouchers/<int:id>/edit-pro', methods=['GET'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def edit_voucher_professional(id, user_area):
    """编辑财务凭证 - 专业编辑器版本"""
    voucher = FinancialVoucher.query.filter_by(
        id=id,
        area_id=user_area.id
    ).first_or_404()

    # 已审核的凭证不允许编辑
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许编辑', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()

    try:
        return render_template('financial/vouchers/edit_professional.html',
                             voucher=voucher,
                             details=details)
    except Exception as e:
        current_app.logger.error(f"渲染专业编辑器模板失败: {str(e)}")
        flash(f'页面加载失败: {str(e)}', 'danger')
        return redirect(url_for('financial.edit_voucher', id=id))


@financial_bp.route('/vouchers/<int:id>/text-view')
@login_required
@school_required
@check_permission('财务报表', 'view')
def voucher_text_view(id, user_area):
    """凭证文本视图"""
    voucher = FinancialVoucher.query.filter_by(
        id=id,
        area_id=user_area.id
    ).first_or_404()

    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()

    # 获取用户信息
    from app.models.auth import User
    created_by_user = User.query.get(voucher.created_by) if voucher.created_by else None
    reviewed_by_user = User.query.get(voucher.reviewed_by) if voucher.reviewed_by else None
    posted_by_user = User.query.get(voucher.posted_by) if voucher.posted_by else None

    # 准备数据
    voucher_data = {
        'voucher_number': voucher.voucher_number,
        'voucher_type': voucher.voucher_type,
        'voucher_date': voucher.voucher_date.strftime('%Y-%m-%d'),
        'attachment_count': voucher.attachment_count or 0,
        'summary': voucher.summary or '',
        'created_by': created_by_user.username if created_by_user else '',
        'reviewed_by': reviewed_by_user.username if reviewed_by_user else '',
        'posted_by': posted_by_user.username if posted_by_user else '',
        'details': []
    }

    for detail in details:
        detail_data = {
            'summary': detail.summary,
            'debit_amount': float(detail.debit_amount),
            'credit_amount': float(detail.credit_amount),
            'subject': {
                'code': detail.subject.code if detail.subject else '',
                'name': detail.subject.name if detail.subject else ''
            }
        }
        voucher_data['details'].append(detail_data)

    # 生成文本格式
    text_processor = FinancialTextProcessor()
    voucher_text = text_processor.format_voucher_text(voucher_data)

    # 生成 Beancount 格式
    beancount_integration = BeanCountIntegration()
    beancount_text = beancount_integration.convert_voucher_to_beancount(voucher_data)

    return render_template('financial/vouchers/text_view.html',
                         voucher=voucher,
                         voucher_text=voucher_text,
                         beancount_text=beancount_text)


@financial_bp.route('/vouchers/<int:id>/review', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'review')
def review_voucher(id, user_area):
    """审核财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id,
        area_id=user_area.id
    ).first_or_404()

    if voucher.status != '待审核':
        flash('只能审核待审核状态的凭证', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    # 检查凭证明细是否平衡
    details = VoucherDetail.query.filter_by(voucher_id=id).all()
    if not details:
        flash('凭证没有明细，无法审核', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    total_debit = sum(detail.debit_amount for detail in details)
    total_credit = sum(detail.credit_amount for detail in details)

    if abs(total_debit - total_credit) > 0.01:  # 允许0.01的误差
        flash('凭证借贷不平衡，无法审核', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    try:
        # 使用 ORM 更新审核状态
        voucher.status = '已审核'
        voucher.reviewed_by = current_user.id
        voucher.reviewed_at = datetime.now()

        db.session.commit()

        flash('凭证审核成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核凭证失败: {str(e)}")
        flash('审核失败，请重试', 'danger')

    return redirect(url_for('financial.view_voucher', id=id))


@financial_bp.route('/vouchers/<int:id>', methods=['PUT'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def update_voucher_api(id, user_area):
    """更新财务凭证API（用于AJAX调用）"""
    voucher = FinancialVoucher.query.filter_by(
        id=id,
        area_id=user_area.id
    ).first_or_404()

    # 已审核的凭证不允许编辑
    if voucher.status in ['已审核', '已记账']:
        return jsonify({'success': False, 'message': '已审核的凭证不允许编辑'})

    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的数据'})

        # 更新凭证基本信息
        if 'voucher_type' in data:
            voucher.voucher_type = data['voucher_type']
        if 'voucher_date' in data:
            from datetime import datetime
            voucher.voucher_date = datetime.strptime(data['voucher_date'], '%Y-%m-%d').date()
        if 'summary' in data:
            voucher.summary = data['summary']
        if 'notes' in data:
            voucher.notes = data['notes']
        if 'attachment_count' in data:
            voucher.attachment_count = int(data['attachment_count'])

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '凭证更新成功',
            'voucher': {
                'id': voucher.id,
                'voucher_type': voucher.voucher_type,
                'voucher_date': voucher.voucher_date.strftime('%Y-%m-%d'),
                'summary': voucher.summary,
                'notes': voucher.notes,
                'attachment_count': voucher.attachment_count,
                'status': voucher.status
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新财务凭证失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})


# ==================== 凭证明细管理 API ====================

@financial_bp.route('/vouchers/<int:voucher_id>/details', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def add_voucher_detail(voucher_id, user_area):
    """添加凭证明细"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 移除状态限制，允许所有状态的凭证添加明细
    # if voucher.status != '草稿':
    #     return jsonify({'success': False, 'message': '只有草稿状态的凭证才能添加明细'})

    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['subject_id', 'summary']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填字段: {field}'})

        # 使用专业财务工具验证明细数据
        detail_data = [{
            'debit_amount': data.get('debit_amount', 0),
            'credit_amount': data.get('credit_amount', 0)
        }]

        is_valid, error_msg = financial_calculator.validate_voucher_balance(detail_data)
        if not is_valid:
            return jsonify({'success': False, 'message': error_msg})

        debit_amount = float(data.get('debit_amount', 0))
        credit_amount = float(data.get('credit_amount', 0))

        # 验证会计科目是否存在（支持系统科目和学校科目）
        from app.models_financial import AccountingSubject
        from app.models import AdministrativeArea

        # 获取系统区域ID（最小的区域ID）
        system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
        system_area_id = system_area.id if system_area else 0

        subject = AccountingSubject.query.filter(
            AccountingSubject.id == data['subject_id'],
            db.or_(
                # 系统科目
                db.and_(
                    AccountingSubject.area_id == system_area_id,
                    AccountingSubject.is_system == True
                ),
                # 学校科目
                db.and_(
                    AccountingSubject.area_id == user_area.id,
                    AccountingSubject.is_system == False
                )
            ),
            AccountingSubject.is_active == True
        ).first()

        if not subject:
            return jsonify({'success': False, 'message': '会计科目不存在或已停用'})

        # 获取下一个行号
        max_line_number = db.session.query(
            func.max(VoucherDetail.line_number)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        # 使用原生SQL创建明细
        insert_sql = text("""
            INSERT INTO voucher_details
            (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount, auxiliary_info)
            VALUES
            (:voucher_id, :line_number, :subject_id, :summary, :debit_amount, :credit_amount, :auxiliary_info)
        """)

        params = {
            'voucher_id': voucher_id,
            'line_number': max_line_number + 1,
            'subject_id': data['subject_id'],
            'summary': data['summary'],
            'debit_amount': debit_amount,
            'credit_amount': credit_amount,
            'auxiliary_info': data.get('auxiliary_info', '')
        }

        db.session.execute(insert_sql, params)

        # 更新凭证总金额
        total_debit = db.session.query(
            func.sum(VoucherDetail.debit_amount)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        voucher.total_amount = float(total_debit)

        db.session.commit()

        # 获取新插入的明细ID
        new_detail = VoucherDetail.query.filter_by(
            voucher_id=voucher_id,
            line_number=max_line_number + 1
        ).first()

        return jsonify({
            'success': True,
            'message': '明细添加成功',
            'detail_id': new_detail.id if new_detail else None,
            'detail': {
                'id': new_detail.id if new_detail else None,
                'line_number': max_line_number + 1,
                'subject_id': data['subject_id'],
                'subject_name': f"{subject.code} - {subject.name}",
                'summary': data['summary'],
                'debit_amount': float(debit_amount),
                'credit_amount': float(credit_amount),
                'auxiliary_info': data.get('auxiliary_info', '')
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details/<int:detail_id>', methods=['PUT'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def update_voucher_detail(voucher_id, detail_id, user_area):
    """更新凭证明细"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 移除状态限制，允许所有状态的凭证修改明细
    # if voucher.status != '草稿':
    #     return jsonify({'success': False, 'message': '只有草稿状态的凭证才能修改明细'})

    detail = VoucherDetail.query.filter_by(
        id=detail_id,
        voucher_id=voucher_id
    ).first_or_404()

    try:
        data = request.get_json()

        # 验证借贷金额
        debit_amount = float(data.get('debit_amount', 0))
        credit_amount = float(data.get('credit_amount', 0))

        if debit_amount <= 0 and credit_amount <= 0:
            return jsonify({'success': False, 'message': '借方金额或贷方金额至少有一个大于0'})

        if debit_amount > 0 and credit_amount > 0:
            return jsonify({'success': False, 'message': '借方金额和贷方金额不能同时大于0'})

        # 验证会计科目（支持系统科目和学校科目）
        if 'subject_id' in data:
            from app.models_financial import AccountingSubject
            from app.models import AdministrativeArea

            # 获取系统区域ID（最小的区域ID）
            system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
            system_area_id = system_area.id if system_area else 0

            subject = AccountingSubject.query.filter(
                AccountingSubject.id == data['subject_id'],
                db.or_(
                    # 系统科目
                    db.and_(
                        AccountingSubject.area_id == system_area_id,
                        AccountingSubject.is_system == True
                    ),
                    # 学校科目
                    db.and_(
                        AccountingSubject.area_id == user_area.id,
                        AccountingSubject.is_system == False
                    )
                ),
                AccountingSubject.is_active == True
            ).first()

            if not subject:
                return jsonify({'success': False, 'message': '会计科目不存在或已停用'})

        # 使用原生SQL更新明细
        update_sql = text("""
            UPDATE voucher_details
            SET subject_id = :subject_id,
                summary = :summary,
                debit_amount = :debit_amount,
                credit_amount = :credit_amount,
                auxiliary_info = :auxiliary_info
            WHERE id = :detail_id
        """)

        params = {
            'detail_id': detail_id,
            'subject_id': data.get('subject_id', detail.subject_id),
            'summary': data.get('summary', detail.summary),
            'debit_amount': debit_amount,
            'credit_amount': credit_amount,
            'auxiliary_info': data.get('auxiliary_info', detail.auxiliary_info or '')
        }

        db.session.execute(update_sql, params)

        # 更新凭证总金额
        total_debit = db.session.query(
            func.sum(VoucherDetail.debit_amount)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        voucher.total_amount = float(total_debit)

        db.session.commit()

        # 获取更新后的明细信息
        updated_detail = VoucherDetail.query.get(detail_id)
        subject = AccountingSubject.query.get(updated_detail.subject_id)

        return jsonify({
            'success': True,
            'message': '明细更新成功',
            'detail': {
                'id': updated_detail.id,
                'line_number': updated_detail.line_number,
                'subject_id': updated_detail.subject_id,
                'subject_name': f"{subject.code} - {subject.name}" if subject else '',
                'summary': updated_detail.summary,
                'debit_amount': float(updated_detail.debit_amount),
                'credit_amount': float(updated_detail.credit_amount),
                'auxiliary_info': updated_detail.auxiliary_info or ''
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details/<int:detail_id>', methods=['GET'])
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def get_voucher_detail(voucher_id, detail_id, user_area):
    """获取凭证明细信息"""
    try:
        # 验证凭证是否存在且属于当前学校
        voucher = FinancialVoucher.query.filter_by(
            id=voucher_id,
            area_id=user_area.id
        ).first_or_404()

        # 获取明细
        detail = VoucherDetail.query.filter_by(
            id=detail_id,
            voucher_id=voucher_id
        ).first()

        if not detail:
            return jsonify({'success': False, 'message': '明细不存在'})

        return jsonify({
            'success': True,
            'detail': {
                'id': detail.id,
                'subject_id': detail.subject_id,
                'summary': detail.summary,
                'debit_amount': float(detail.debit_amount),
                'credit_amount': float(detail.credit_amount),
                'auxiliary_info': detail.auxiliary_info
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details/<int:detail_id>', methods=['DELETE'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def delete_voucher_detail(voucher_id, detail_id, user_area):
    """删除凭证明细"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 移除状态限制，允许所有状态的凭证删除明细
    # if voucher.status != '草稿':
    #     return jsonify({'success': False, 'message': '只有草稿状态的凭证才能删除明细'})

    detail = VoucherDetail.query.filter_by(
        id=detail_id,
        voucher_id=voucher_id
    ).first_or_404()

    try:
        # 使用原生SQL删除明细
        delete_sql = text("DELETE FROM voucher_details WHERE id = :detail_id")
        db.session.execute(delete_sql, {'detail_id': detail_id})

        # 重新排序行号
        reorder_sql = text("""
            UPDATE voucher_details
            SET line_number = new_line_number
            FROM (
                SELECT id, ROW_NUMBER() OVER (ORDER BY line_number) as new_line_number
                FROM voucher_details
                WHERE voucher_id = :voucher_id
            ) as numbered
            WHERE voucher_details.id = numbered.id
        """)
        db.session.execute(reorder_sql, {'voucher_id': voucher_id})

        # 更新凭证总金额
        total_debit = db.session.query(
            func.sum(VoucherDetail.debit_amount)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        voucher.total_amount = float(total_debit)

        db.session.commit()

        return jsonify({'success': True, 'message': '明细删除成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details', methods=['GET'])
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def get_voucher_details(voucher_id, user_area):
    """获取凭证明细列表（用于专业编辑器）"""
    try:
        # 验证凭证是否存在且属于当前学校
        voucher = FinancialVoucher.query.filter_by(
            id=voucher_id,
            area_id=user_area.id
        ).first_or_404()

        # 获取明细列表
        details = VoucherDetail.query.filter_by(
            voucher_id=voucher_id
        ).order_by(VoucherDetail.line_number).all()

        details_data = []
        for detail in details:
            subject = detail.subject
            details_data.append({
                'id': detail.id,
                'line_number': detail.line_number,
                'subject_id': detail.subject_id,
                'subject': {
                    'id': subject.id,
                    'code': subject.code,
                    'name': subject.name
                } if subject else None,
                'summary': detail.summary,
                'debit_amount': float(detail.debit_amount),
                'credit_amount': float(detail.credit_amount),
                'auxiliary_info': detail.auxiliary_info or ''
            })

        return jsonify({
            'success': True,
            'details': details_data,
            'voucher': {
                'id': voucher.id,
                'voucher_number': voucher.voucher_number,
                'voucher_type': voucher.voucher_type,
                'voucher_date': voucher.voucher_date.strftime('%Y-%m-%d'),
                'status': voucher.status,
                'total_amount': float(voucher.total_amount)
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取凭证明细列表失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/submit-review', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def submit_voucher_for_review(voucher_id, user_area):
    """提交凭证审核"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 只有草稿状态的凭证才能提交审核
    if voucher.status != '草稿':
        return jsonify({'success': False, 'message': '只有草稿状态的凭证才能提交审核'})

    # 检查是否有明细
    details = VoucherDetail.query.filter_by(voucher_id=voucher_id).all()
    if not details:
        return jsonify({'success': False, 'message': '凭证没有明细，无法提交审核'})

    # 使用专业财务工具检查借贷平衡
    details_data = [{
        'debit_amount': float(detail.debit_amount),
        'credit_amount': float(detail.credit_amount)
    } for detail in details]

    is_balanced, balance_error = financial_calculator.validate_voucher_balance(details_data)
    if not is_balanced:
        return jsonify({
            'success': False,
            'message': f'凭证借贷不平衡，无法提交审核。{balance_error}'
        })

    try:
        # 更新凭证状态
        voucher.status = '待审核'
        db.session.commit()

        return jsonify({'success': True, 'message': '凭证已提交审核'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"提交凭证审核失败: {str(e)}")
        return jsonify({'success': False, 'message': f'提交失败: {str(e)}'})





@financial_bp.route('/vouchers/pending-stock-ins')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def pending_stock_ins_for_voucher(user_area):
    """待生成凭证的入库单列表"""

    # 查询已财务确认但未生成凭证的入库单
    from app.models import StockIn
    stock_ins = StockIn.query.filter(
        StockIn.area_id == user_area.id,
        StockIn.status == '已入库',
        StockIn.voucher_id.is_(None),
        StockIn.total_cost > 0
    ).order_by(StockIn.created_at.desc()).all()

    return render_template('financial/vouchers/pending_stock_ins.html',
                         stock_ins=stock_ins, user_area=user_area)


@financial_bp.route('/vouchers/generate-from-stock-in', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def generate_voucher_from_stock_in(user_area):
    """从单个入库单生成财务凭证"""
    stock_in_id = request.json.get('stock_in_id')

    if not stock_in_id:
        return jsonify({'success': False, 'message': '请选择入库单'})

    # 检查入库单是否存在且属于当前学校
    from app.models import StockIn
    stock_in = StockIn.query.filter_by(
        id=stock_in_id,
        area_id=user_area.id
    ).first()

    if not stock_in:
        return jsonify({'success': False, 'message': '入库单不存在'})

    # 检查是否已生成凭证
    if stock_in.voucher_id:
        return jsonify({'success': False, 'message': '该入库单已生成财务凭证'})

    # 检查是否财务确认
    if stock_in.status != '已入库':
        return jsonify({'success': False, 'message': '入库单未入库，无法生成凭证'})

    try:
        result = create_voucher_from_stock_in(stock_in, user_area)
        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"从入库单生成凭证失败: {str(e)}")
        return jsonify({'success': False, 'message': '生成失败，请重试'})


@financial_bp.route('/vouchers/batch-generate-from-stock-ins', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def batch_generate_vouchers_from_stock_ins(user_area):
    """批量从入库单生成财务凭证"""
    start_date = request.json.get('start_date')
    end_date = request.json.get('end_date')
    auto_review = request.json.get('auto_review', True)

    if not start_date or not end_date:
        return jsonify({'success': False, 'message': '请选择日期范围'})

    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'success': False, 'message': '日期格式错误'})

    try:
        # 查询指定日期范围内已财务确认但未生成凭证的入库单
        from app.models import StockIn
        stock_ins = StockIn.query.filter(
            StockIn.area_id == user_area.id,
            StockIn.status == '已入库',
            StockIn.voucher_id.is_(None),
            StockIn.total_cost > 0,
            StockIn.created_at >= start_date_obj,
            StockIn.created_at <= end_date_obj
        ).all()

        if not stock_ins:
            return jsonify({
                'success': True,
                'message': '指定日期范围内没有待生成凭证的入库单',
                'success_count': 0,
                'failed_count': 0,
                'total_amount': 0
            })

        success_count = 0
        failed_count = 0
        total_amount = 0

        for stock_in in stock_ins:
            try:
                result = create_voucher_from_stock_in(stock_in, user_area, auto_review)
                if result['success']:
                    success_count += 1
                    total_amount += float(stock_in.total_cost)
                else:
                    failed_count += 1
                    current_app.logger.warning(f"入库单 {stock_in.stock_in_number} 生成凭证失败: {result['message']}")
            except Exception as e:
                failed_count += 1
                current_app.logger.error(f"入库单 {stock_in.stock_in_number} 生成凭证异常: {str(e)}")

        return jsonify({
            'success': True,
            'message': f'批量生成完成，成功 {success_count} 个，失败 {failed_count} 个',
            'success_count': success_count,
            'failed_count': failed_count,
            'total_amount': total_amount
        })

    except Exception as e:
        current_app.logger.error(f"批量生成凭证失败: {str(e)}")
        return jsonify({'success': False, 'message': '批量生成失败，请重试'})


def create_voucher_from_stock_in(stock_in, user_area, auto_review=True):
    """从入库单创建财务凭证的核心逻辑"""
    try:
        # 生成凭证号（使用新的生成函数）
        voucher_number = generate_voucher_number(user_area, date.today())

        # 使用原生SQL创建财务凭证，使用命名参数
        insert_sql = text("""
            INSERT INTO financial_vouchers
            (voucher_number, voucher_date, area_id, voucher_type, summary,
             total_amount, status, source_type, source_id, attachment_count,
             created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
            VALUES
            (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
             :total_amount, :status, :source_type, :source_id, :attachment_count,
             :created_by, :reviewed_by, :reviewed_at, :posted_by, :posted_at, :notes)
        """)

        # 确保日期类型正确
        voucher_date = stock_in.stock_in_date
        if isinstance(voucher_date, datetime):
            voucher_date = voucher_date.date()

        # 确保时间戳类型正确
        reviewed_at = None
        if auto_review:
            reviewed_at = datetime.now().replace(microsecond=0)

        params = {
            'voucher_number': voucher_number,
            'voucher_date': voucher_date,
            'area_id': user_area.id,
            'voucher_type': '入库凭证',
            'summary': f'入库单{stock_in.stock_in_number}',
            'total_amount': Decimal(str(stock_in.total_cost)),
            'status': '已审核' if auto_review else '待审核',
            'source_type': '入库单',
            'source_id': stock_in.id,
            'attachment_count': 0,
            'created_by': stock_in.operator_id,
            'reviewed_by': stock_in.operator_id if auto_review else None,
            'reviewed_at': reviewed_at,
            'posted_by': None,
            'posted_at': None,
            'notes': f'自动生成自入库单{stock_in.stock_in_number}'
        }

        # 执行插入
        db.session.execute(insert_sql, params)

        # 获取刚插入的记录ID
        select_sql = text("""
            SELECT id FROM financial_vouchers
            WHERE voucher_number = :voucher_number AND area_id = :area_id
        """)
        result = db.session.execute(select_sql, {
            'voucher_number': voucher_number,
            'area_id': user_area.id
        })
        voucher_id = result.fetchone()[0]

        # 获取会计科目
        inventory_subject = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            code='1402'  # 原材料
        ).first()

        payable_subject = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            code='2201'  # 应付账款
        ).first()

        if not inventory_subject:
            db.session.rollback()
            return {'success': False, 'message': '未找到原材料科目(1402)，请先设置会计科目'}

        if not payable_subject:
            db.session.rollback()
            return {'success': False, 'message': '未找到应付账款科目(2201)，请先设置会计科目'}

        # 使用原生SQL生成凭证明细
        detail_sql = text("""
            INSERT INTO voucher_details
            (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount)
            VALUES
            (:voucher_id, :line_number, :subject_id, :summary, :debit_amount, :credit_amount)
        """)

        # 借：原材料
        detail1_params = {
            'voucher_id': voucher_id,
            'line_number': 1,
            'subject_id': inventory_subject.id,
            'summary': f'入库单{stock_in.stock_in_number}',
            'debit_amount': Decimal(str(stock_in.total_cost)),
            'credit_amount': Decimal('0.00')
        }
        db.session.execute(detail_sql, detail1_params)

        # 贷：应付账款
        detail2_params = {
            'voucher_id': voucher_id,
            'line_number': 2,
            'subject_id': payable_subject.id,
            'summary': f'入库单{stock_in.stock_in_number}',
            'debit_amount': Decimal('0.00'),
            'credit_amount': Decimal(str(stock_in.total_cost))
        }
        db.session.execute(detail_sql, detail2_params)

        # 更新入库单关联信息
        stock_in.voucher_id = voucher_id

        db.session.commit()

        return {
            'success': True,
            'message': '财务凭证生成成功',
            'voucher_id': voucher_id,
            'voucher_number': voucher_number
        }

    except Exception as e:
        db.session.rollback()
        raise e


# ==================== 财务报表生成 ====================

@financial_bp.route('/reports/trial-balance')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def trial_balance_report(user_area):
    """试算平衡表"""
    # 获取查询参数
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    export_format = request.args.get('format', 'html')  # html 或 excel

    # 默认查询当月数据
    if not start_date or not end_date:
        from datetime import date
        today = date.today()
        start_date = today.replace(day=1).strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')

    try:
        # 查询凭证明细数据
        query = text("""
            SELECT
                s.code as account_code,
                s.name as account_name,
                s.subject_type,
                vd.debit_amount,
                vd.credit_amount
            FROM voucher_details vd
            JOIN accounting_subjects s ON vd.subject_id = s.id
            JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = :area_id
            AND fv.voucher_date BETWEEN :start_date AND :end_date
            AND fv.status IN ('已审核', '已记账')
            ORDER BY s.code
        """)

        result = db.session.execute(query, {
            'area_id': user_area.id,
            'start_date': start_date,
            'end_date': end_date
        })

        # 转换为字典列表
        voucher_details = []
        for row in result:
            voucher_details.append({
                'account_code': row.account_code,
                'account_name': row.account_name,
                'subject_type': row.subject_type,
                'debit_amount': float(row.debit_amount or 0),
                'credit_amount': float(row.credit_amount or 0)
            })

        # 使用专业财务工具生成试算平衡表
        from datetime import datetime
        period_start = datetime.strptime(start_date, '%Y-%m-%d').date()
        period_end = datetime.strptime(end_date, '%Y-%m-%d').date()

        trial_balance_df = report_generator.generate_trial_balance(
            voucher_details, period_start, period_end
        )

        if export_format == 'excel':
            # 导出Excel
            import tempfile
            import os
            from flask import send_file

            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            filename = temp_file.name
            temp_file.close()

            success = report_generator.export_to_excel(
                trial_balance_df,
                filename,
                f'试算平衡表_{start_date}至{end_date}'
            )

            if success:
                return send_file(
                    filename,
                    as_attachment=True,
                    download_name=f'试算平衡表_{start_date}至{end_date}.xlsx',
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            else:
                flash('导出Excel失败', 'danger')

        # 转换DataFrame为HTML表格数据
        if not trial_balance_df.empty:
            table_data = trial_balance_df.to_dict('records')
        else:
            table_data = []

        return render_template('financial/reports/trial_balance.html',
                             table_data=table_data,
                             start_date=start_date,
                             end_date=end_date,
                             user_area=user_area)

    except Exception as e:
        current_app.logger.error(f"生成试算平衡表失败: {str(e)}")
        flash(f'生成报表失败: {str(e)}', 'danger')
        return render_template('financial/reports/trial_balance.html',
                             table_data=[],
                             start_date=start_date,
                             end_date=end_date,
                             user_area=user_area)
