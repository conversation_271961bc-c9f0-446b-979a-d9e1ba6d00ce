{% extends "financial/base.html" %}

{% block title %}科目余额表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">科目余额表</h3>
                </div>
                <div class="card-body">
                    <!-- 查询条件 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="balance_date" class="form-label">余额日期</label>
                                <input type="date" class="form-control" id="balance_date" name="balance_date" 
                                       value="{{ balance_date }}" required>
                            </div>
                            <div class="col-md-4">
                                <label for="subject_type" class="form-label">科目类型</label>
                                <select class="form-select" id="subject_type" name="subject_type">
                                    <option value="">全部类型</option>
                                    <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                                    <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                                    <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                                    <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                                    <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="exportBalanceSheet()">
                                        <i class="fas fa-file-excel"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 科目余额表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>科目编码</th>
                                    <th>科目名称</th>
                                    <th>科目类型</th>
                                    <th>余额方向</th>
                                    <th>级次</th>
                                    <th class="text-right">累计借方</th>
                                    <th class="text-right">累计贷方</th>
                                    <th class="text-right">余额</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set total_debit = 0 %}
                                {% set total_credit = 0 %}
                                {% set total_balance = 0 %}
                                
                                {% for item in balance_data %}
                                {% set total_debit = total_debit + item.total_debit %}
                                {% set total_credit = total_credit + item.total_credit %}
                                {% set total_balance = total_balance + item.balance %}
                                
                                <tr {% if item.level == 1 %}class="table-info"{% endif %}>
                                    <td>
                                        {% for i in range(item.level - 1) %}&nbsp;&nbsp;&nbsp;&nbsp;{% endfor %}
                                        {{ item.code }}
                                    </td>
                                    <td>
                                        {% for i in range(item.level - 1) %}&nbsp;&nbsp;&nbsp;&nbsp;{% endfor %}
                                        {% if item.level == 1 %}<strong>{{ item.name }}</strong>{% else %}{{ item.name }}{% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ item.subject_type }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {% if item.balance_direction == '借方' %}bg-primary{% else %}bg-warning{% endif %}">
                                            {{ item.balance_direction }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ item.level }}</span>
                                    </td>
                                    <td class="text-right">
                                        {% if item.total_debit > 0 %}
                                            {{ "%.2f"|format(item.total_debit) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if item.total_credit > 0 %}
                                            {{ "%.2f"|format(item.total_credit) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if item.balance != 0 %}
                                            <strong 
                                                {% if item.balance > 0 %}class="text-primary"{% else %}class="text-danger"{% endif %}>
                                                {{ "%.2f"|format(item.balance) }}
                                            </strong>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('financial.detail_ledger', subject_id=item.id, end_date=balance_date) }}" 
                                           class="btn btn-sm btn-outline-primary" title="查看明细账">
                                            <i class="fas fa-list"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not balance_data %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        没有找到相关数据
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                            
                            {% if balance_data %}
                            <tfoot class="table-secondary">
                                <tr>
                                    <th colspan="5">合计</th>
                                    <th class="text-right">{{ "%.2f"|format(total_debit) }}</th>
                                    <th class="text-right">{{ "%.2f"|format(total_credit) }}</th>
                                    <th class="text-right">{{ "%.2f"|format(total_balance) }}</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                            {% endif %}
                        </table>
                    </div>

                    <!-- 余额分析 -->
                    {% if balance_data %}
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>余额统计</h6>
                                    <p><strong>科目总数：</strong>{{ balance_data|length }}</p>
                                    <p><strong>有余额科目：</strong>{{ balance_data|selectattr('balance', '!=', 0)|list|length }}</p>
                                    <p><strong>累计借方发生额：</strong>{{ "%.2f"|format(total_debit) }}</p>
                                    <p><strong>累计贷方发生额：</strong>{{ "%.2f"|format(total_credit) }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>按科目类型统计</h6>
                                    {% set type_stats = {} %}
                                    {% for item in balance_data %}
                                        {% if item.subject_type not in type_stats %}
                                            {% set _ = type_stats.update({item.subject_type: {'count': 0, 'balance': 0}}) %}
                                        {% endif %}
                                        {% set _ = type_stats[item.subject_type].update({
                                            'count': type_stats[item.subject_type]['count'] + 1,
                                            'balance': type_stats[item.subject_type]['balance'] + item.balance
                                        }) %}
                                    {% endfor %}
                                    
                                    {% for type_name, stats in type_stats.items() %}
                                    <p><strong>{{ type_name }}：</strong>{{ stats.count }}个科目，余额合计 {{ "%.2f"|format(stats.balance) }}</p>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 说明信息 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 说明</h6>
                                <ul class="mb-0">
                                    <li>科目余额表显示截至指定日期的所有科目余额情况</li>
                                    <li>累计借方/贷方：从建账开始到查询日期的累计发生额</li>
                                    <li>余额：累计借方发生额 - 累计贷方发生额</li>
                                    <li>一级科目以粗体显示，下级科目按层级缩进</li>
                                    <li>点击"查看明细账"可以查看该科目的详细发生记录</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
function exportBalanceSheet() {
    const balanceDate = document.getElementById('balance_date').value;
    const subjectType = document.getElementById('subject_type').value;
    
    const url = `{{ url_for('financial.export_report', report_type='balance_detail') }}?balance_date=${balanceDate}&subject_type=${subjectType}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
