{% extends "base.html" %}

{% block title %}故障排除{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('help.index') }}">帮助中心</a></li>
            <li class="breadcrumb-item active">故障排除</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h1><i class="fas fa-exclamation-triangle"></i> 故障排除</h1>
                    <p class="lead">常见问题解决方案和技术支持</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 紧急联系 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <h5><i class="fas fa-phone"></i> 紧急技术支持</h5>
                <p class="mb-0">如果遇到紧急问题，请立即联系技术支持：<strong>18373062333</strong></p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 主要内容 -->
        <div class="col-lg-8">
            <!-- 常见问题 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-question-circle"></i> 常见问题与解决方案</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="troubleshootingAccordion">
                        <!-- 会计科目问题 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="accounting-issues">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-accounting">
                                    <i class="fas fa-chart-line text-danger me-2"></i>
                                    会计科目下拉框为空或无法添加凭证明细
                                </button>
                            </h2>
                            <div id="collapse-accounting" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <div class="alert alert-warning">
                                        <strong>症状：</strong>在财务凭证页面点击"添加明细"时，会计科目下拉框为空
                                    </div>
                                    <strong>解决步骤：</strong>
                                    <ol>
                                        <li>打开 <a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
                                        <li>点击"检查状态"查看当前科目情况</li>
                                        <li>如果系统科目数量为0，点击"初始化系统科目"或"修复数据结构"</li>
                                        <li>等待操作完成，确认系统科目数量大于0</li>
                                        <li>回到凭证页面重新测试添加明细功能</li>
                                    </ol>
                                    <div class="alert alert-success mt-3">
                                        <i class="fas fa-lightbulb"></i> <strong>预防措施：</strong>定期检查会计科目状态，确保系统科目完整
                                    </div>
                                    <div class="mt-3">
                                        <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-book"></i> 详细指南
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 登录问题 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="login-issues">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-login">
                                    <i class="fas fa-sign-in-alt text-warning me-2"></i>
                                    无法登录或权限不足
                                </button>
                            </h2>
                            <div id="collapse-login" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <div class="alert alert-info">
                                        <strong>常见原因：</strong>用户名密码错误、账号被禁用、权限不足
                                    </div>
                                    <strong>解决方案：</strong>
                                    <ul>
                                        <li><strong>忘记密码：</strong>联系系统管理员重置密码</li>
                                        <li><strong>权限不足：</strong>联系学校管理员分配相应权限</li>
                                        <li><strong>账号问题：</strong>确认账号状态是否正常</li>
                                        <li><strong>新用户：</strong>首次使用需要管理员创建账号</li>
                                    </ul>
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-info-circle"></i> <strong>注意：</strong>连续登录失败可能导致账号被临时锁定
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据同步问题 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="sync-issues">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-sync">
                                    <i class="fas fa-sync-alt text-info me-2"></i>
                                    数据不同步或显示异常
                                </button>
                            </h2>
                            <div id="collapse-sync" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <strong>常见情况：</strong>
                                    <ul>
                                        <li><strong>页面数据不更新：</strong>刷新页面（Ctrl+F5 强制刷新）</li>
                                        <li><strong>库存数据异常：</strong>检查入库出库记录是否正确</li>
                                        <li><strong>菜单显示错误：</strong>确认周菜单配置是否完整</li>
                                        <li><strong>权限变更未生效：</strong>重新登录系统</li>
                                    </ul>
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-exclamation-triangle"></i> 如问题持续存在，请联系技术支持
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 打印问题 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="print-issues">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-print">
                                    <i class="fas fa-print text-success me-2"></i>
                                    打印功能异常
                                </button>
                            </h2>
                            <div id="collapse-print" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <strong>解决步骤：</strong>
                                    <ol>
                                        <li>检查浏览器是否允许弹出窗口</li>
                                        <li>确认打印机连接正常</li>
                                        <li>尝试使用不同浏览器（推荐Chrome）</li>
                                        <li>检查打印预览是否正常显示</li>
                                        <li>如果是PDF打印，确认PDF阅读器正常</li>
                                    </ol>
                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle"></i> <strong>推荐浏览器：</strong>Chrome、Edge、Firefox
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 性能问题 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="performance-issues">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-performance">
                                    <i class="fas fa-tachometer-alt text-primary me-2"></i>
                                    系统运行缓慢
                                </button>
                            </h2>
                            <div id="collapse-performance" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <strong>可能原因和解决方案：</strong>
                                    <ul>
                                        <li><strong>网络问题：</strong>检查网络连接，尝试刷新页面</li>
                                        <li><strong>浏览器缓存：</strong>清除浏览器缓存和Cookie</li>
                                        <li><strong>数据量过大：</strong>使用筛选条件减少查询数据量</li>
                                        <li><strong>并发用户过多：</strong>避免高峰期使用</li>
                                    </ul>
                                    <div class="alert alert-success mt-3">
                                        <i class="fas fa-lightbulb"></i> <strong>优化建议：</strong>定期清理浏览器缓存，使用筛选功能
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系支持 -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-headset"></i> 技术支持</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-phone"></i> 电话支持</h6>
                            <p>技术支持热线：<strong>18373062333</strong><br>
                            服务时间：工作日 9:00-18:00</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-envelope"></i> 邮件支持</h6>
                            <p>技术支持邮箱：<strong><EMAIL></strong><br>
                            响应时间：24小时内回复</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 快速解决 -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-bolt"></i> 快速解决</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-tools"></i> 会计科目修复
                        </a>
                        <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-book"></i> 科目管理指南
                        </a>
                        <button class="btn btn-outline-secondary btn-sm" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> 刷新页面
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="window.location.href=window.location.href">
                            <i class="fas fa-redo"></i> 重新加载
                        </button>
                    </div>
                </div>
            </div>

            <!-- 相关帮助 -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-book"></i> 相关帮助</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{{ url_for('help.financial_help') }}" class="text-decoration-none">
                                <i class="fas fa-calculator text-primary"></i> 财务管理帮助
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('help.accounting_subjects_help') }}" class="text-decoration-none">
                                <i class="fas fa-chart-line text-success"></i> 会计科目管理
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('help.index') }}" class="text-decoration-none">
                                <i class="fas fa-home text-info"></i> 帮助中心首页
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
