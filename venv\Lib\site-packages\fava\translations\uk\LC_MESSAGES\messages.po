msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: uk\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Помилки"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Час"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Рахунок"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Отримувач"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Доходний звіт"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Баланс"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Оборотно-сальдова відомість"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Журнал"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Запит"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Запаси"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Цінності"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Події"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Редактор"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Налаштування"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Статистика"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Допомога"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Дата"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "Ф"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Ціна"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Вартість"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Зміна"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Баланс"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Зберегти"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Зберігається..."

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Вміст файлу змінився. Натисніть для перезавантаження."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Розкрити усі рахунки"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Розкрити усе"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Журнал Рахунку"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Зміни"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Баланс"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Перейти до файлу %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Файл"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Строка"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Помилка"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Помилок не знайдено."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Подія: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Опис"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Сторінки допомоги"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Валюта"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Курс валюти"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Запаси у"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Чистий дохід"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Дохід"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Витрати"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Ключ"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Значення"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Скачати як"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Проведення по Рахункам"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Загалом"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Оновлення"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Клікніть щоб скопіювати балансові директиви за рахунками (виключаючи зелені) у буфер обміну."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Скопіювати строки балансу"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Останній Запис"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Записи за Типом"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Тип"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "Записи"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Опис"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Кількість"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Імпортувати"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Бюджетні записи"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Джерело"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Витягти"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "...введіть BQL запит. 'help' щоб отримати список доступних команд."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Метадані"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Додати метадані"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Примітка"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Розташування"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Контекст"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Баланс до запису"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Баланс після запису"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Уточнені транзакції"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Очікувані транзакції"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Інші транзакції"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Документи з #discovered тегом"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Записи"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Завантажити файл(и)"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Завантаження"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Вирівняти суми"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Закоментувати виділене"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Відкрити усі згортання"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Закрити усі згортання"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Налаштування Fava"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "допомога"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Налаштування Beancount"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Відправити"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Клік з натисненим Shift щоб розкрити усі рівні.\n"
"Клік з натисненим Ctrl або Cmd щоб розкрити один рівень."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Експортувати"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Фільтрувати за тегом, отримувачем, ..."

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Завантажити поточно відфільтровані записи як файл Beancount"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Щорічно"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Щоквартально"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Щомісячно"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Щотижнево"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Щоденно"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Редагувати"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Сума"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Документи з #linked тегом"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "За вартістю"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "За ринковою вартістю"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "Одиниці"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "Treemap"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "Кільцева діаграма"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "Мусить бути одним з задекларованих акаунтів"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "Попередній"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "Наступний"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "Транзакція"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "Додати"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "Перемістити чи пеерйменувати документ"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "Ім'я"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "Документи"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "Видалити"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "Лінійна діаграма"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "Діаграма з областями"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "Продовжити"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "Очистити"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "Перейти до акаунту"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr ""

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr ""

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr ""

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr ""

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr ""

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr ""

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr ""

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr ""

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

