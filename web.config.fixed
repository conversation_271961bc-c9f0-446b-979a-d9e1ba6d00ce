<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- URL重写规则 - 反向代理到Flask应用 -->
        <rewrite>
            <!-- 允许设置的服务器变量 - 解决HTTP 500.50错误 -->
            <allowedServerVariables>
                <add name="HTTP_X_FORWARDED_FOR" />
                <add name="HTTP_X_FORWARDED_PROTO" />
                <add name="HTTP_X_FORWARDED_HOST" />
                <add name="HTTP_X_REAL_IP" />
            </allowedServerVariables>
            
            <rules>
                <rule name="ReverseProxyInboundRule1" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Rewrite" url="http://127.0.0.1:8080/{R:1}" />
                    <serverVariables>
                        <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
                        <set name="HTTP_X_FORWARDED_PROTO" value="http" />
                        <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
                    </serverVariables>
                </rule>
            </rules>
        </rewrite>

        <!-- 默认文档设置 -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.html" />
            </files>
        </defaultDocument>

        <!-- HTTP错误页面 -->
        <httpErrors errorMode="Detailed" />

        <!-- 安全头设置 -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Frame-Options" value="SAMEORIGIN" />
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-XSS-Protection" value="1; mode=block" />
            </customHeaders>
        </httpProtocol>

        <!-- 静态文件处理 -->
        <staticContent>
            <mimeMap fileExtension=".json" mimeType="application/json" />
            <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
            <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
        </staticContent>

        <!-- 压缩设置 -->
        <urlCompression doStaticCompression="true" doDynamicCompression="true" />
        
        <!-- 缓存设置 -->
        <caching>
            <profiles>
                <add extension=".css" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                <add extension=".js" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                <add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
                <add extension=".jpg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
                <add extension=".gif" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
            </profiles>
        </caching>
    </system.webServer>
</configuration>
