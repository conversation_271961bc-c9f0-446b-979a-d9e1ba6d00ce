{% extends "financial/base.html" %}

{% block page_title %}会计科目管理{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">会计科目管理</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.create_accounting_subject') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 新增学校科目
    </a>
    <a href="{{ url_for('financial.accounting_subjects_text_tree') }}" class="btn btn-secondary">
        <i class="fas fa-sitemap"></i> 文本树视图
    </a>
    <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-info">
        <i class="fas fa-question-circle"></i> 帮助中心
    </a>
</div>
{% endblock %}

{% block financial_content %}
<!-- 搜索表单 -->
<div class="financial-search-form">
    <form method="GET" action="{{ url_for('financial.accounting_subjects_index') }}">
        <div class="form-row">
            <div class="form-group col-md-3">
                <label for="keyword">关键词</label>
                <input type="text" class="form-control" id="keyword" name="keyword" 
                       value="{{ keyword }}" placeholder="科目编码或名称">
            </div>
            <div class="form-group col-md-3">
                <label for="subject_type">科目类型</label>
                <select class="form-control" id="subject_type" name="subject_type">
                    <option value="">全部类型</option>
                    <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                    <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                    <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                    <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                    <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                </select>
            </div>
            <div class="form-group col-md-3">
                <button type="submit" class="btn btn-info">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary">
                    <i class="fas fa-undo"></i> 重置
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 科目列表 -->
<div class="financial-card">
    <div class="financial-card-header">
        <i class="fas fa-list"></i> 会计科目列表
        <span class="badge badge-info ml-2">共 {{ subjects.total }} 条记录</span>
    </div>
    <div class="financial-card-body">
        {% if subjects.items %}
        <div class="table-responsive">
            <table class="table table-bordered financial-table">
                <thead>
                    <tr>
                        <th width="10%">科目编码</th>
                        <th width="20%">科目名称</th>
                        <th width="10%">科目类型</th>
                        <th width="10%">余额方向</th>
                        <th width="8%">级别</th>
                        <th width="15%">上级科目</th>
                        <th width="8%">状态</th>
                        <th width="8%">系统科目</th>
                        <th width="11%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for subject in subjects.items %}
                    <tr>
                        <td>
                            <code>{{ subject.code }}</code>
                        </td>
                        <td>
                            <strong>{{ subject.name }}</strong>
                            {% if subject.description %}
                            <br><small class="text-muted">{{ subject.description }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge badge-secondary">{{ subject.subject_type }}</span>
                        </td>
                        <td>
                            {% if subject.balance_direction == '借方' %}
                            <span class="badge badge-primary">借方</span>
                            {% else %}
                            <span class="badge badge-success">贷方</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge badge-info">{{ subject.level }}级</span>
                        </td>
                        <td>
                            {% if subject.parent %}
                            <small>{{ subject.parent.code }} - {{ subject.parent.name }}</small>
                            {% else %}
                            <span class="text-muted">无</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if subject.is_active %}
                            <span class="badge badge-success">启用</span>
                            {% else %}
                            <span class="badge badge-warning">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if subject.is_system %}
                            <span class="badge badge-info">系统</span>
                            {% else %}
                            <span class="badge badge-light">自定义</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if not subject.is_system %}
                                <a href="{{ url_for('financial.edit_accounting_subject', id=subject.id) }}" 
                                   class="btn btn-outline-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ url_for('financial.delete_accounting_subject', id=subject.id) }}" 
                                      style="display: inline;">
                                    <button type="submit" class="btn btn-outline-danger" 
                                            data-confirm-delete="确定要删除科目 {{ subject.name }} 吗？"
                                            title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% else %}
                                <span class="text-muted small">系统科目</span>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if subjects.pages > 1 %}
        <nav aria-label="科目分页">
            <ul class="pagination financial-pagination">
                {% if subjects.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('financial.accounting_subjects_index', 
                        page=subjects.prev_num, keyword=keyword, subject_type=subject_type) }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in subjects.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != subjects.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('financial.accounting_subjects_index', 
                                page=page_num, keyword=keyword, subject_type=subject_type) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if subjects.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('financial.accounting_subjects_index', 
                        page=subjects.next_num, keyword=keyword, subject_type=subject_type) }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <p class="text-muted">暂无会计科目数据</p>
            <div class="alert alert-warning mx-auto" style="max-width: 600px;">
                <h6><i class="fas fa-exclamation-triangle"></i> 系统异常</h6>
                <p class="mb-3">会计科目应该在学校注册时自动创建，如果您看到此消息，说明系统可能存在问题。</p>
                <p class="mb-3">请联系系统管理员或查看帮助中心获取解决方案。</p>
                <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-info">
                    <i class="fas fa-question-circle"></i> 查看帮助中心
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 科目类型说明 -->
<div class="financial-card">
    <div class="financial-card-header">
        <i class="fas fa-info-circle"></i> 会计科目说明
    </div>
    <div class="financial-card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-coins text-success"></i> 资产类科目</h6>
                <p class="small text-muted">
                    记录学校食堂拥有的各种资产，如现金、银行存款、库存商品、固定资产等。
                    余额方向为借方。
                </p>
                
                <h6><i class="fas fa-credit-card text-danger"></i> 负债类科目</h6>
                <p class="small text-muted">
                    记录学校食堂的各种债务，如应付账款、应付职工薪酬等。
                    余额方向为贷方。
                </p>
                
                <h6><i class="fas fa-balance-scale text-info"></i> 所有者权益类科目</h6>
                <p class="small text-muted">
                    记录学校食堂的净资产，如实收资本、本年利润等。
                    余额方向为贷方。
                </p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-arrow-up text-success"></i> 收入类科目</h6>
                <p class="small text-muted">
                    记录学校食堂的各种收入，如学生餐费收入、教师餐费收入等。
                    余额方向为贷方。
                </p>
                
                <h6><i class="fas fa-arrow-down text-warning"></i> 费用类科目</h6>
                <p class="small text-muted">
                    记录学校食堂的各种支出，如食材成本、人工费用、管理费用等。
                    余额方向为借方。
                </p>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>使用说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统已预设了标准的会计科目体系，显示为"系统"标识</li>
                        <li>您可以在系统科目下新建子科目，也可以新建独立的学校科目</li>
                        <li>学校科目显示为"自定义"标识，可以编辑和删除</li>
                        <li>系统科目不能编辑或删除，确保会计标准的一致性</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 会计科目管理页面特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 科目类型筛选变化时自动提交表单
    document.getElementById('subject_type').addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %}
