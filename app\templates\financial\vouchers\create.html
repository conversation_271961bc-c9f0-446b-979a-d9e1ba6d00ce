{% extends 'base.html' %}

{% block title %}新建记账凭证 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 用友风格专业凭证编辑器样式 */
.voucher-container {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 10px;
}

.voucher-window {
    background: white;
    border: 1px solid #ccc;
    border-radius: 0;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
    margin: 0 auto;
    max-width: 1200px;
}

.voucher-header {
    background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
    border-bottom: 1px solid #ccc;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.voucher-toolbar {
    background: #f8f8f8;
    border-bottom: 1px solid #ddd;
    padding: 5px 10px;
    display: flex;
    gap: 5px;
    align-items: center;
}

.toolbar-btn {
    background: linear-gradient(to bottom, #ffffff, #e6e6e6);
    border: 1px solid #adadad;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
}

.toolbar-btn:hover {
    background: linear-gradient(to bottom, #f0f0f0, #d0d0d0);
}

.toolbar-btn.active {
    background: linear-gradient(to bottom, #d0d0d0, #b0b0b0);
    border-color: #999;
}

.voucher-info-bar {
    background: #f0f0f0;
    border-bottom: 1px solid #ddd;
    padding: 8px 15px;
    display: grid;
    grid-template-columns: auto auto auto auto 1fr auto;
    gap: 20px;
    align-items: center;
    font-size: 12px;
}

.info-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.info-label {
    color: #666;
    font-weight: normal;
}

.info-input {
    border: 1px solid #ccc;
    padding: 2px 5px;
    font-size: 12px;
    background: white;
}

.voucher-table-container {
    padding: 0;
    background: white;
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    margin: 0;
}

.voucher-table th {
    background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
    border: 1px solid #ccc;
    padding: 6px 4px;
    text-align: center;
    font-weight: normal;
    color: #333;
    font-size: 12px;
}

.voucher-table td {
    border: 1px solid #ddd;
    padding: 2px;
    vertical-align: middle;
    background: white;
}

.voucher-table tbody tr:hover {
    background: #f0f8ff;
}

.voucher-table tbody tr.selected {
    background: #e0e8f0;
}

.cell-input {
    border: none;
    background: transparent;
    width: 100%;
    padding: 4px;
    font-size: 12px;
    outline: none;
    font-family: inherit;
}

.cell-input:focus {
    background: #fffacd;
    border: 1px solid #4a90e2;
}

.amount-input {
    text-align: right;
    font-family: 'Courier New', monospace;
}

.line-number {
    text-align: center;
    color: #666;
    background: #f8f8f8;
    width: 30px;
}

.subject-cell {
    position: relative;
    min-width: 200px;
}

.subject-selector {
    display: flex;
    align-items: center;
    gap: 2px;
}

.subject-code {
    width: 60px;
    text-align: center;
}

.subject-name {
    flex: 1;
}

.subject-btn {
    background: #f0f0f0;
    border: 1px solid #ccc;
    padding: 2px 4px;
    cursor: pointer;
    font-size: 10px;
}

.totals-row {
    background: #f0f8ff;
    font-weight: bold;
}

.totals-row td {
    border-top: 2px solid #333;
}

.balance-status {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.balance-ok {
    background: #d4edda;
    color: #155724;
}

.balance-error {
    background: #f8d7da;
    color: #721c24;
}

.signature-area {
    background: #f8f8f8;
    border-top: 1px solid #ddd;
    padding: 10px 15px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    font-size: 12px;
}

.signature-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.signature-label {
    color: #666;
    min-width: 40px;
}

.signature-box {
    border-bottom: 1px solid #999;
    min-width: 80px;
    height: 20px;
    display: inline-block;
}

/* 状态栏 */
.status-bar {
    background: #f0f0f0;
    border-top: 1px solid #ddd;
    padding: 4px 15px;
    font-size: 11px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .voucher-info-bar {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .signature-area {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .voucher-table {
        font-size: 11px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="voucher-container">
    <div class="voucher-window">
        <!-- 窗口标题栏 -->
        <div class="voucher-header">
            <span>记账凭证</span>
            <div class="window-controls">
                <button class="toolbar-btn" onclick="window.history.back()">✕ 关闭</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="voucher-toolbar">
            <button class="toolbar-btn" onclick="saveVoucher()">💾 保存</button>
            <button class="toolbar-btn" onclick="addRow()">➕ 增行</button>
            <button class="toolbar-btn" onclick="deleteRow()">➖ 删行</button>
            <button class="toolbar-btn" onclick="insertRow()">📝 插行</button>
            <button class="toolbar-btn" onclick="copyRow()">📋 复制</button>
            <button class="toolbar-btn" onclick="checkBalance()">⚖️ 平衡</button>
            <div style="margin-left: auto;">
                <span class="balance-status" id="balance-indicator">未检查</span>
            </div>
        </div>

        <!-- 凭证信息栏 -->
        <div class="voucher-info-bar">
            <div class="info-group">
                <span class="info-label">凭证字:</span>
                <select class="info-input" id="voucher-type" style="width: 80px;">
                    <option value="记">记</option>
                    <option value="收">收</option>
                    <option value="付">付</option>
                    <option value="转">转</option>
                </select>
            </div>
            <div class="info-group">
                <span class="info-label">号:</span>
                <input type="text" class="info-input" id="voucher-number" style="width: 60px;" placeholder="自动">
            </div>
            <div class="info-group">
                <span class="info-label">日期:</span>
                <input type="date" class="info-input" id="voucher-date" value="{{ today }}">
            </div>
            <div class="info-group">
                <span class="info-label">附件:</span>
                <input type="number" class="info-input" id="attachment-count" style="width: 40px;" value="0" min="0">
                <span class="info-label">张</span>
            </div>
            <div style="flex: 1;"></div>
            <div class="info-group">
                <span class="info-label">制单人:</span>
                <span>{{ current_user.username }}</span>
            </div>
        </div>

        <!-- 凭证表格 -->
        <div class="voucher-table-container">
            <table class="voucher-table" id="voucher-table">
                <thead>
                    <tr>
                        <th style="width: 30px;">序号</th>
                        <th style="width: 200px;">摘要</th>
                        <th style="width: 250px;">会计科目</th>
                        <th style="width: 80px;">借方金额</th>
                        <th style="width: 80px;">贷方金额</th>
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
                    <!-- 凭证行将在这里动态生成 -->
                </tbody>
                <tfoot>
                    <tr class="totals-row">
                        <td colspan="3" style="text-align: center; font-weight: bold;">合计</td>
                        <td class="amount-input" id="debit-total">0.00</td>
                        <td class="amount-input" id="credit-total">0.00</td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- 签字区域 -->
        <div class="signature-area">
            <div class="signature-item">
                <span class="signature-label">制单:</span>
                <span class="signature-box">{{ current_user.username }}</span>
            </div>
            <div class="signature-item">
                <span class="signature-label">审核:</span>
                <span class="signature-box"></span>
            </div>
            <div class="signature-item">
                <span class="signature-label">记账:</span>
                <span class="signature-box"></span>
            </div>
            <div class="signature-item">
                <span class="signature-label">出纳:</span>
                <span class="signature-box"></span>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>就绪</span>
            <span id="current-time"></span>
        </div>
    </div>
</div>

<!-- 科目选择模态框 -->
<div class="modal fade" id="subjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择会计科目</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="text" class="form-control" id="subject-search" placeholder="搜索科目编码或名称...">
                </div>
                <div class="table-responsive" style="max-height: 400px;">
                    <table class="table table-sm table-hover" id="subject-table">
                        <thead>
                            <tr>
                                <th>科目编码</th>
                                <th>科目名称</th>
                                <th>科目类型</th>
                            </tr>
                        </thead>
                        <tbody id="subject-tbody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let currentRow = 0;
let currentCol = 0;
let subjects = [];
let selectedSubjectCell = null;

$(document).ready(function() {
    // 初始化
    initVoucherEditor();
    loadSubjects();
    updateTime();
    setInterval(updateTime, 1000);

    // 绑定事件
    bindEvents();

    // 添加第一行
    addRow();
});

function initVoucherEditor() {
    console.log('初始化用友风格凭证编辑器');

    // 设置今天日期
    const today = new Date().toISOString().split('T')[0];
    $('#voucher-date').val(today);
}

function bindEvents() {
    // 键盘导航
    $(document).on('keydown', '.cell-input', function(e) {
        handleKeyNavigation(e, this);
    });

    // 金额输入事件
    $(document).on('input', '.amount-input', function() {
        updateTotals();
        checkBalance();
    });

    // 科目搜索
    $('#subject-search').on('input', function() {
        filterSubjects($(this).val());
    });

    // 科目选择
    $(document).on('click', '#subject-tbody tr', function() {
        selectSubject(this);
    });

    // 快捷键
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveVoucher();
        }
        if (e.key === 'F9') {
            e.preventDefault();
            checkBalance();
        }
    });
}

function handleKeyNavigation(e, element) {
    const $element = $(element);
    const $row = $element.closest('tr');
    const $cell = $element.closest('td');
    const rowIndex = $row.index();
    const cellIndex = $cell.index();

    switch(e.key) {
        case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
                moveToPreviousCell(rowIndex, cellIndex);
            } else {
                moveToNextCell(rowIndex, cellIndex);
            }
            break;
        case 'Enter':
            e.preventDefault();
            moveToNextRow(rowIndex, cellIndex);
            break;
        case 'ArrowUp':
            e.preventDefault();
            moveToPreviousRow(rowIndex, cellIndex);
            break;
        case 'ArrowDown':
            e.preventDefault();
            moveToNextRow(rowIndex, cellIndex);
            break;
    }
}

function moveToNextCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex < 4) { // 移动到下一列
        const $nextCell = $currentRow.find('td').eq(cellIndex + 1).find('.cell-input');
        if ($nextCell.length) {
            $nextCell.focus().select();
        }
    } else { // 移动到下一行第一列
        moveToNextRow(rowIndex, 0);
    }
}

function moveToPreviousCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex > 1) { // 移动到上一列
        const $prevCell = $currentRow.find('td').eq(cellIndex - 1).find('.cell-input');
        if ($prevCell.length) {
            $prevCell.focus().select();
        }
    } else if (rowIndex > 0) { // 移动到上一行最后一列
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $lastCell = $prevRow.find('td').eq(4).find('.cell-input');
        if ($lastCell.length) {
            $lastCell.focus().select();
        }
    }
}

function moveToNextRow(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    let $nextRow = $tbody.find('tr').eq(rowIndex + 1);

    if ($nextRow.length === 0) {
        // 如果没有下一行，自动添加新行
        addRow();
        $nextRow = $tbody.find('tr').last();
    }

    const targetCellIndex = Math.max(1, cellIndex); // 至少从摘要列开始
    const $targetCell = $nextRow.find('td').eq(targetCellIndex).find('.cell-input');
    if ($targetCell.length) {
        $targetCell.focus().select();
    }
}

function moveToPreviousRow(rowIndex, cellIndex) {
    if (rowIndex > 0) {
        const $tbody = $('#voucher-tbody');
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $targetCell = $prevRow.find('td').eq(cellIndex).find('.cell-input');
        if ($targetCell.length) {
            $targetCell.focus().select();
        }
    }
}

function addRow() {
    const $tbody = $('#voucher-tbody');
    const rowNumber = $tbody.find('tr').length + 1;

    const rowHtml = `
        <tr data-row="${rowNumber}">
            <td class="line-number">${rowNumber}</td>
            <td>
                <input type="text" class="cell-input summary-input" placeholder="摘要">
            </td>
            <td class="subject-cell">
                <div class="subject-selector">
                    <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                    <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                    <input type="hidden" class="subject-id">
                </div>
            </td>
            <td>
                <input type="number" class="cell-input amount-input debit-amount"
                       step="0.01" min="0" placeholder="0.00">
            </td>
            <td>
                <input type="number" class="cell-input amount-input credit-amount"
                       step="0.01" min="0" placeholder="0.00">
            </td>
        </tr>
    `;

    $tbody.append(rowHtml);
    updateRowNumbers();
}

function deleteRow() {
    const $tbody = $('#voucher-tbody');
    const $rows = $tbody.find('tr');

    if ($rows.length > 1) {
        $rows.last().remove();
        updateRowNumbers();
        updateTotals();
        checkBalance();
    }
}

function insertRow() {
    // 在当前焦点行之后插入新行
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const rowNumber = $currentRow.index() + 1;

        const rowHtml = `
            <tr data-row="${rowNumber}">
                <td class="line-number">${rowNumber}</td>
                <td>
                    <input type="text" class="cell-input summary-input" placeholder="摘要">
                </td>
                <td class="subject-cell">
                    <div class="subject-selector">
                        <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                        <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                        <input type="hidden" class="subject-id">
                    </div>
                </td>
                <td>
                    <input type="number" class="cell-input amount-input debit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
                <td>
                    <input type="number" class="cell-input amount-input credit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
            </tr>
        `;

        $currentRow.after(rowHtml);
        updateRowNumbers();
    } else {
        addRow();
    }
}

function copyRow() {
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const $newRow = $currentRow.clone();

        // 清空新行的序号和ID
        $newRow.find('.line-number').text('');
        $newRow.removeAttr('data-row');

        $currentRow.after($newRow);
        updateRowNumbers();
    }
}

function updateRowNumbers() {
    $('#voucher-tbody tr').each(function(index) {
        $(this).find('.line-number').text(index + 1);
        $(this).attr('data-row', index + 1);
    });
}

function updateTotals() {
    let debitTotal = 0;
    let creditTotal = 0;

    $('.debit-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        debitTotal += value;
    });

    $('.credit-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        creditTotal += value;
    });

    $('#debit-total').text(debitTotal.toFixed(2));
    $('#credit-total').text(creditTotal.toFixed(2));
}

function checkBalance() {
    const debitTotal = parseFloat($('#debit-total').text()) || 0;
    const creditTotal = parseFloat($('#credit-total').text()) || 0;
    const difference = Math.abs(debitTotal - creditTotal);

    const $indicator = $('#balance-indicator');

    if (difference < 0.01) {
        $indicator.removeClass('balance-error').addClass('balance-ok').text('借贷平衡');
    } else {
        $indicator.removeClass('balance-ok').addClass('balance-error').text(`不平衡 差额:${difference.toFixed(2)}`);
    }
}

function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    $('#current-time').text(timeString);
}

function loadSubjects() {
    $.ajax({
        url: '/financial/subjects/api',
        type: 'GET',
        success: function(response) {
            if (response.success) {
                subjects = response.subjects;
                console.log('科目数据加载成功:', subjects.length);
            } else {
                console.error('加载科目失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载科目失败:', error);
        }
    });
}

function openSubjectModal(element) {
    selectedSubjectCell = $(element).closest('.subject-selector');
    $('#subjectModal').modal('show');
    renderSubjects(subjects);
    $('#subject-search').focus();
}

function renderSubjects(subjectList) {
    const $tbody = $('#subject-tbody');
    $tbody.empty();

    subjectList.forEach(function(subject) {
        const row = `
            <tr data-subject-id="${subject.id}" data-subject-code="${subject.code}"
                data-subject-name="${subject.name}" style="cursor: pointer;">
                <td>${subject.code}</td>
                <td>${subject.name}</td>
                <td>${subject.subject_type}</td>
            </tr>
        `;
        $tbody.append(row);
    });
}

function filterSubjects(searchTerm) {
    if (!searchTerm) {
        renderSubjects(subjects);
        return;
    }

    const filtered = subjects.filter(function(subject) {
        return subject.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
               subject.name.toLowerCase().includes(searchTerm.toLowerCase());
    });

    renderSubjects(filtered);
}

function selectSubject(row) {
    const $row = $(row);
    const subjectId = $row.data('subject-id');
    const subjectCode = $row.data('subject-code');
    const subjectName = $row.data('subject-name');

    if (selectedSubjectCell) {
        selectedSubjectCell.find('.subject-code').val(subjectCode);
        selectedSubjectCell.find('.subject-name').val(subjectName);
        selectedSubjectCell.find('.subject-id').val(subjectId);
    }

    $('#subjectModal').modal('hide');
    selectedSubjectCell = null;
}

function saveVoucher() {
    const voucherData = collectVoucherData();

    if (!validateVoucherData(voucherData)) {
        return;
    }

    $.ajax({
        url: '/financial/vouchers/create',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(voucherData),
        success: function(response) {
            if (response.success) {
                alert('凭证保存成功！');
                window.location.href = '/financial/vouchers/' + response.voucher_id;
            } else {
                alert('保存失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('保存失败，请重试');
            console.error('保存失败:', error);
        }
    });
}

function collectVoucherData() {
    const details = [];

    $('#voucher-tbody tr').each(function() {
        const $row = $(this);
        const summary = $row.find('.summary-input').val();
        const subjectId = $row.find('.subject-id').val();
        const debitAmount = parseFloat($row.find('.debit-amount').val()) || 0;
        const creditAmount = parseFloat($row.find('.credit-amount').val()) || 0;

        if (summary || subjectId || debitAmount > 0 || creditAmount > 0) {
            details.push({
                summary: summary,
                subject_id: subjectId,
                debit_amount: debitAmount,
                credit_amount: creditAmount
            });
        }
    });

    return {
        voucher_type: $('#voucher-type').val(),
        voucher_date: $('#voucher-date').val(),
        attachment_count: parseInt($('#attachment-count').val()) || 0,
        details: details
    };
}

function validateVoucherData(data) {
    if (data.details.length === 0) {
        alert('请至少添加一行凭证明细');
        return false;
    }

    let debitTotal = 0;
    let creditTotal = 0;

    for (let detail of data.details) {
        if (!detail.subject_id) {
            alert('请为所有明细行选择会计科目');
            return false;
        }

        if (detail.debit_amount === 0 && detail.credit_amount === 0) {
            alert('每行明细必须有借方或贷方金额');
            return false;
        }

        if (detail.debit_amount > 0 && detail.credit_amount > 0) {
            alert('每行明细不能同时有借方和贷方金额');
            return false;
        }

        debitTotal += detail.debit_amount;
        creditTotal += detail.credit_amount;
    }

    if (Math.abs(debitTotal - creditTotal) > 0.01) {
        alert('借贷不平衡，请检查金额');
        return false;
    }

    return true;
}
</script>
{% endblock %}
