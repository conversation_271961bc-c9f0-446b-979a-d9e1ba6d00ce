2025-06-08 12:27:45,516 INFO: 应用启动 - PID: 12124 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-08 12:28:15,454 INFO: 表单提交数据: ImmutableMultiDict([('voucher_date', '2025-06-08'), ('voucher_type', '入库凭证'), ('summary', '33333333333'), ('notes', 'AAA')]) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:90]
2025-06-08 12:28:15,454 INFO: 表单验证结果: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:91]
2025-06-08 12:28:15,455 INFO: 表单错误: {} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:92]
2025-06-08 12:28:15,469 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO financial_vouchers (voucher_number, voucher_date, area_id, voucher_type, summary, total_amount, status, source_type, source_id, attachment_count, created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PZ20250608001', datetime.datetime(2025, 6, 8, 0, 0), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', None, 0, 34, None, None, None, None, 'AAA', datetime.datetime(2025, 6, 8, 12, 28, 15), datetime.datetime(2025, 6, 8, 12, 28, 15))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:138]
2025-06-08 12:28:15,470 ERROR: 详细错误信息: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py", line 127, in create_voucher
    db.session.flush()  # 获取ID但不提交
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\scoping.py", line 937, in flush
    return self._proxied.flush(objects=objects)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4341, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4477, in _flush
    transaction.rollback(_capture_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4437, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO financial_vouchers (voucher_number, voucher_date, area_id, voucher_type, summary, total_amount, status, source_type, source_id, attachment_count, created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PZ20250608001', datetime.datetime(2025, 6, 8, 0, 0), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', None, 0, 34, None, None, None, None, 'AAA', datetime.datetime(2025, 6, 8, 12, 28, 15), datetime.datetime(2025, 6, 8, 12, 28, 15))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:139]
2025-06-08 12:29:31,056 INFO: 表单提交数据: ImmutableMultiDict([('voucher_date', '2025-06-08'), ('voucher_type', '入库凭证'), ('summary', '33333333333'), ('notes', 'AAA')]) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:90]
2025-06-08 12:29:31,058 INFO: 表单验证结果: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:91]
2025-06-08 12:29:31,058 INFO: 表单错误: {} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:92]
2025-06-08 12:29:31,061 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO financial_vouchers (voucher_number, voucher_date, area_id, voucher_type, summary, total_amount, status, source_type, source_id, attachment_count, created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PZ20250608001', datetime.datetime(2025, 6, 8, 0, 0), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', None, 0, 34, None, None, None, None, 'AAA', datetime.datetime(2025, 6, 8, 12, 29, 31), datetime.datetime(2025, 6, 8, 12, 29, 31))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:138]
2025-06-08 12:29:31,061 ERROR: 详细错误信息: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py", line 127, in create_voucher
    params = {
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\scoping.py", line 937, in flush
    return self._proxied.flush(objects=objects)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4341, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4477, in _flush
    transaction.rollback(_capture_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4437, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO financial_vouchers (voucher_number, voucher_date, area_id, voucher_type, summary, total_amount, status, source_type, source_id, attachment_count, created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PZ20250608001', datetime.datetime(2025, 6, 8, 0, 0), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', None, 0, 34, None, None, None, None, 'AAA', datetime.datetime(2025, 6, 8, 12, 29, 31), datetime.datetime(2025, 6, 8, 12, 29, 31))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:139]
2025-06-08 12:30:06,113 INFO: 表单提交数据: ImmutableMultiDict([('voucher_date', '2025-06-08'), ('voucher_type', '入库凭证'), ('summary', '33333333333'), ('notes', '梦梦梦梦梦梦s')]) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:90]
2025-06-08 12:30:06,113 INFO: 表单验证结果: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:91]
2025-06-08 12:30:06,114 INFO: 表单错误: {} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:92]
2025-06-08 12:30:06,119 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO financial_vouchers (voucher_number, voucher_date, area_id, voucher_type, summary, total_amount, status, source_type, source_id, attachment_count, created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PZ20250608001', datetime.datetime(2025, 6, 8, 0, 0), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', None, 0, 34, None, None, None, None, '梦梦梦梦梦梦s', datetime.datetime(2025, 6, 8, 12, 30, 6), datetime.datetime(2025, 6, 8, 12, 30, 6))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:138]
2025-06-08 12:30:06,120 ERROR: 详细错误信息: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py", line 127, in create_voucher
    params = {
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\scoping.py", line 937, in flush
    return self._proxied.flush(objects=objects)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4341, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4477, in _flush
    transaction.rollback(_capture_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4437, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO financial_vouchers (voucher_number, voucher_date, area_id, voucher_type, summary, total_amount, status, source_type, source_id, attachment_count, created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('PZ20250608001', datetime.datetime(2025, 6, 8, 0, 0), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', None, 0, 34, None, None, None, None, '梦梦梦梦梦梦s', datetime.datetime(2025, 6, 8, 12, 30, 6), datetime.datetime(2025, 6, 8, 12, 30, 6))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:139]
