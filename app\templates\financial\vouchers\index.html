{% extends "financial/base.html" %}

{% block title %}财务凭证管理{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">财务凭证管理</h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-plus"></i> 新建凭证
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="{{ url_for('financial.create_voucher') }}">
                                    <i class="fas fa-edit"></i> 手工录入
                                </a>
                                <a class="dropdown-item" href="{{ url_for('financial.pending_stock_ins_for_voucher') }}">
                                    <i class="fas fa-magic"></i> 从入库单生成
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" onclick="showTemplateModal()">
                                    <i class="fas fa-copy"></i> 从模板创建
                                </a>
                                <a class="dropdown-item" href="#" onclick="showImportModal()">
                                    <i class="fas fa-upload"></i> 导入凭证
                                </a>
                            </div>
                        </div>

                        <div class="btn-group ml-2">
                            <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-cogs"></i> 批量操作
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="#" onclick="showBatchGenerateModal()">
                                    <i class="fas fa-magic"></i> 批量生成
                                </a>
                                <a class="dropdown-item" href="#" onclick="showBatchReviewModal()">
                                    <i class="fas fa-check-double"></i> 批量审核
                                </a>
                                <a class="dropdown-item" href="#" onclick="showBatchDeleteModal()">
                                    <i class="fas fa-trash-alt"></i> 批量删除
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" onclick="showBatchExportModal()">
                                    <i class="fas fa-download"></i> 批量导出
                                </a>
                            </div>
                        </div>

                        <div class="btn-group ml-2">
                            <button type="button" class="btn btn-secondary btn-sm" onclick="toggleAdvancedSearch()">
                                <i class="fas fa-search-plus"></i> 高级搜索
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="showStatsModal()">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 数据统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">总凭证数</h6>
                                            <h4 class="mb-0">{{ vouchers.total }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-file-invoice fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">待审核</h6>
                                            <h4 class="mb-0">{{ pending_count or 0 }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">已审核</h6>
                                            <h4 class="mb-0">{{ approved_count or 0 }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">本月金额</h6>
                                            <h4 class="mb-0">¥{{ "%.0f"|format(month_amount or 0) }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-coins fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 基础搜索表单 -->
                    <form method="GET" class="mb-4" id="searchForm">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control form-control-sm" id="keyword" name="keyword"
                                           value="{{ keyword }}" placeholder="凭证号或摘要">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="voucher_type">凭证类型</label>
                                    <select class="form-control form-control-sm" id="voucher_type" name="voucher_type">
                                        <option value="">-- 所有类型 --</option>
                                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款凭证</option>
                                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款凭证</option>
                                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账凭证</option>
                                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账凭证</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">状态</label>
                                    <select class="form-control form-control-sm" id="status" name="status">
                                        <option value="">-- 所有状态 --</option>
                                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">开始日期</label>
                                    <input type="date" class="form-control form-control-sm" id="start_date" name="start_date"
                                           value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">结束日期</label>
                                    <input type="date" class="form-control form-control-sm" id="end_date" name="end_date"
                                           value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-undo"></i> 重置
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 高级搜索区域 -->
                        <div id="advancedSearch" style="display: none;">
                            <hr>
                            <h6><i class="fas fa-search-plus"></i> 高级搜索</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="creator">创建人</label>
                                        <input type="text" class="form-control form-control-sm" id="creator" name="creator"
                                               value="{{ creator }}" placeholder="创建人姓名">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="min_amount">最小金额</label>
                                        <input type="number" class="form-control form-control-sm" id="min_amount" name="min_amount"
                                               value="{{ min_amount }}" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="max_amount">最大金额</label>
                                        <input type="number" class="form-control form-control-sm" id="max_amount" name="max_amount"
                                               value="{{ max_amount }}" step="0.01" placeholder="999999.99">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="subject_code">科目编码</label>
                                        <input type="text" class="form-control form-control-sm" id="subject_code" name="subject_code"
                                               value="{{ subject_code }}" placeholder="会计科目编码">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="has_attachment">附件</label>
                                        <select class="form-control form-control-sm" id="has_attachment" name="has_attachment">
                                            <option value="">-- 不限 --</option>
                                            <option value="1" {% if has_attachment == '1' %}selected{% endif %}>有附件</option>
                                            <option value="0" {% if has_attachment == '0' %}selected{% endif %}>无附件</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="sort_by">排序方式</label>
                                        <select class="form-control form-control-sm" id="sort_by" name="sort_by">
                                            <option value="date_desc" {% if sort_by == 'date_desc' %}selected{% endif %}>日期降序</option>
                                            <option value="date_asc" {% if sort_by == 'date_asc' %}selected{% endif %}>日期升序</option>
                                            <option value="amount_desc" {% if sort_by == 'amount_desc' %}selected{% endif %}>金额降序</option>
                                            <option value="amount_asc" {% if sort_by == 'amount_asc' %}selected{% endif %}>金额升序</option>
                                            <option value="number_desc" {% if sort_by == 'number_desc' %}selected{% endif %}>凭证号降序</option>
                                            <option value="number_asc" {% if sort_by == 'number_asc' %}selected{% endif %}>凭证号升序</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="per_page">每页显示</label>
                                        <select class="form-control form-control-sm" id="per_page" name="per_page">
                                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条</option>
                                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条</option>
                                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAdvancedSearch()">
                                                <i class="fas fa-eraser"></i> 清空高级搜索
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 批量操作工具栏 -->
                    {% if vouchers.items %}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                                <label class="form-check-label" for="selectAll">
                                    全选/取消全选
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="btn-group" id="batchActions" style="display: none;">
                                <button type="button" class="btn btn-success btn-sm" onclick="batchReview()">
                                    <i class="fas fa-check"></i> 批量审核
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="batchDelete()">
                                    <i class="fas fa-trash"></i> 批量删除
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="batchExport()">
                                    <i class="fas fa-download"></i> 批量导出
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 凭证列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover" id="vouchersTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th width="3%">
                                        <input type="checkbox" id="selectAllHeader">
                                    </th>
                                    <th width="12%">凭证号</th>
                                    <th width="10%">日期</th>
                                    <th width="8%">类型</th>
                                    <th width="25%">摘要</th>
                                    <th width="12%">金额</th>
                                    <th width="8%">状态</th>
                                    <th width="8%">创建人</th>
                                    <th width="5%">附件</th>
                                    <th width="9%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for voucher in vouchers.items %}
                                <tr data-voucher-id="{{ voucher.id }}" data-status="{{ voucher.status }}">
                                    <td>
                                        <input type="checkbox" class="voucher-checkbox" value="{{ voucher.id }}">
                                    </td>
                                    <td>
                                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="text-decoration-none">
                                            <strong>{{ voucher.voucher_number }}</strong>
                                        </a>
                                    </td>
                                    <td>{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <span class="badge badge-outline-primary">{{ voucher.voucher_type }}</span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ voucher.summary }}">
                                            {{ voucher.summary }}
                                        </div>
                                    </td>
                                    <td class="text-right">
                                        <strong>¥{{ "%.2f"|format(voucher.total_amount) }}</strong>
                                    </td>
                                    <td>
                                        {% if voucher.status == '草稿' %}
                                            <span class="badge badge-secondary">{{ voucher.status }}</span>
                                        {% elif voucher.status == '待审核' %}
                                            <span class="badge badge-warning">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已审核' %}
                                            <span class="badge badge-success">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已记账' %}
                                            <span class="badge badge-primary">{{ voucher.status }}</span>
                                        {% else %}
                                            <span class="badge badge-light">{{ voucher.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ voucher.creator.username if voucher.creator else '未知' }}</small>
                                    </td>
                                    <td class="text-center">
                                        {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                            <span class="badge badge-info">{{ voucher.attachment_count }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-info btn-sm dropdown-toggle"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="{{ url_for('financial.view_voucher', id=voucher.id) }}">
                                                    <i class="fas fa-eye"></i> 查看详情
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}">
                                                    <i class="fas fa-file-alt"></i> 文本视图
                                                </a>
                                                {% if voucher.status in ['草稿', '待审核'] %}
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="{{ url_for('financial.edit_voucher', id=voucher.id) }}">
                                                    <i class="fas fa-edit"></i> 标准编辑
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('financial.edit_voucher_professional', id=voucher.id) }}">
                                                    <i class="fas fa-magic"></i> 专业编辑
                                                </a>
                                                {% endif %}
                                                {% if voucher.status == '待审核' %}
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="#" onclick="reviewVoucher({{ voucher.id }})">
                                                    <i class="fas fa-check text-success"></i> 审核通过
                                                </a>
                                                {% endif %}
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="#" onclick="copyVoucher({{ voucher.id }})">
                                                    <i class="fas fa-copy"></i> 复制凭证
                                                </a>
                                                <a class="dropdown-item" href="#" onclick="exportVoucher({{ voucher.id }})">
                                                    <i class="fas fa-download"></i> 导出
                                                </a>
                                                {% if voucher.status in ['草稿', '待审核'] %}
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item text-danger" href="#" onclick="deleteVoucher({{ voucher.id }})">
                                                    <i class="fas fa-trash"></i> 删除
                                                </a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if vouchers.pages > 1 %}
                    <nav aria-label="凭证分页">
                        <ul class="pagination justify-content-center">
                            {% if vouchers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in vouchers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != vouchers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if vouchers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无财务凭证数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 页面加载完成后初始化
$(document).ready(function() {
    // 初始化表格功能
    initTableFeatures();

    // 初始化日期选择器
    initDatePickers();

    // 绑定搜索表单事件
    bindSearchEvents();
});

// 初始化表格功能
function initTableFeatures() {
    // 全选功能
    $('#selectAll, #selectAllHeader').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.voucher-checkbox').prop('checked', isChecked);
        $('#selectAll, #selectAllHeader').prop('checked', isChecked);
        toggleBatchActions();
    });

    // 单个复选框变化
    $('.voucher-checkbox').on('change', function() {
        const totalCheckboxes = $('.voucher-checkbox').length;
        const checkedCheckboxes = $('.voucher-checkbox:checked').length;

        $('#selectAll, #selectAllHeader').prop('checked', totalCheckboxes === checkedCheckboxes);
        toggleBatchActions();
    });

    // 表格行点击高亮
    $('#vouchersTable tbody tr').on('click', function(e) {
        if (!$(e.target).is('input, button, a')) {
            $(this).toggleClass('table-active');
        }
    });
}

// 切换批量操作按钮显示
function toggleBatchActions() {
    const checkedCount = $('.voucher-checkbox:checked').length;
    if (checkedCount > 0) {
        $('#batchActions').show();
    } else {
        $('#batchActions').hide();
    }
}

// 初始化日期选择器
function initDatePickers() {
    // 设置日期选择器的默认值和限制
    const today = new Date().toISOString().split('T')[0];

    // 如果没有设置日期，默认显示本月
    if (!$('#start_date').val() && !$('#end_date').val()) {
        const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
        $('#start_date').val(firstDay.toISOString().split('T')[0]);
        $('#end_date').val(today);
    }
}

// 绑定搜索事件
function bindSearchEvents() {
    // 状态和类型变化时自动搜索
    $('#voucher_type, #status').on('change', function() {
        $('#searchForm').submit();
    });

    // 回车键搜索
    $('#keyword').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchForm').submit();
        }
    });
}

// 切换高级搜索
function toggleAdvancedSearch() {
    $('#advancedSearch').slideToggle();
}

// 清空高级搜索
function clearAdvancedSearch() {
    $('#advancedSearch input').val('');
    $('#advancedSearch select').prop('selectedIndex', 0);
}

// 显示统计模态框
function showStatsModal() {
    // 这里可以加载统计数据
    alert('统计分析功能开发中...');
}

// 显示模板模态框
function showTemplateModal() {
    alert('模板功能开发中...');
}

// 显示导入模态框
function showImportModal() {
    alert('导入功能开发中...');
}

// 显示批量审核模态框
function showBatchReviewModal() {
    const checkedIds = getCheckedVoucherIds();
    if (checkedIds.length === 0) {
        alert('请先选择要审核的凭证');
        return;
    }

    if (confirm(`确定要批量审核选中的 ${checkedIds.length} 个凭证吗？`)) {
        batchReview();
    }
}

// 显示批量删除模态框
function showBatchDeleteModal() {
    const checkedIds = getCheckedVoucherIds();
    if (checkedIds.length === 0) {
        alert('请先选择要删除的凭证');
        return;
    }

    if (confirm(`确定要批量删除选中的 ${checkedIds.length} 个凭证吗？此操作不可恢复！`)) {
        batchDelete();
    }
}

// 显示批量导出模态框
function showBatchExportModal() {
    const checkedIds = getCheckedVoucherIds();
    if (checkedIds.length === 0) {
        alert('请先选择要导出的凭证');
        return;
    }

    batchExport();
}

// 获取选中的凭证ID
function getCheckedVoucherIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 批量审核
function batchReview() {
    const ids = getCheckedVoucherIds();
    if (ids.length === 0) {
        alert('请先选择要审核的凭证');
        return;
    }

    // 这里实现批量审核逻辑
    console.log('批量审核凭证:', ids);
    alert('批量审核功能开发中...');
}

// 批量删除
function batchDelete() {
    const ids = getCheckedVoucherIds();
    if (ids.length === 0) {
        alert('请先选择要删除的凭证');
        return;
    }

    // 这里实现批量删除逻辑
    console.log('批量删除凭证:', ids);
    alert('批量删除功能开发中...');
}

// 批量导出
function batchExport() {
    const ids = getCheckedVoucherIds();
    if (ids.length === 0) {
        alert('请先选择要导出的凭证');
        return;
    }

    // 这里实现批量导出逻辑
    console.log('批量导出凭证:', ids);
    alert('批量导出功能开发中...');
}

// 单个凭证操作
function reviewVoucher(id) {
    if (confirm('确定要审核此凭证吗？')) {
        // 实现审核逻辑
        console.log('审核凭证:', id);
        alert('审核功能开发中...');
    }
}

function copyVoucher(id) {
    // 实现复制逻辑
    console.log('复制凭证:', id);
    alert('复制功能开发中...');
}

function exportVoucher(id) {
    // 实现导出逻辑
    console.log('导出凭证:', id);
    alert('导出功能开发中...');
}

function deleteVoucher(id) {
    if (confirm('确定要删除此凭证吗？此操作不可恢复！')) {
        // 实现删除逻辑
        console.log('删除凭证:', id);
        alert('删除功能开发中...');
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    // 设置默认日期范围（本月）
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

    // 重置状态
    document.getElementById('batchGenerateProgress').style.display = 'none';
    document.getElementById('batchGenerateResults').style.display = 'none';
    document.getElementById('batchGenerateForm').style.display = 'block';
    document.getElementById('startBatchGenerate').style.display = 'inline-block';

    $('#batchGenerateModal').modal('show');
}

// 开始批量生成
document.getElementById('startBatchGenerate').addEventListener('click', function() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const autoReview = document.getElementById('autoReview').checked;

    if (!startDate || !endDate) {
        alert('请选择日期范围');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能大于结束日期');
        return;
    }

    // 隐藏表单，显示进度
    document.getElementById('batchGenerateForm').style.display = 'none';
    document.getElementById('startBatchGenerate').style.display = 'none';
    document.getElementById('batchGenerateProgress').style.display = 'block';

    // 开始批量生成
    batchGenerateVouchers(startDate, endDate, autoReview);
});

// 批量生成凭证
function batchGenerateVouchers(startDate, endDate, autoReview) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');

    progressText.textContent = '正在查找待生成凭证的入库单...';
    progressBar.style.width = '10%';
    progressBar.textContent = '10%';

    fetch('{{ url_for("financial.batch_generate_vouchers_from_stock_ins") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            auto_review: autoReview
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '生成完成！';

        // 显示结果
        document.getElementById('batchGenerateProgress').style.display = 'none';
        document.getElementById('batchGenerateResults').style.display = 'block';

        const resultsContent = document.getElementById('resultsContent');
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量生成成功</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功生成凭证：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                        <li>总金额：¥${data.total_amount.toFixed(2)}</li>
                    </ul>
                    ${data.failed_count > 0 ? '<p><strong>失败原因：</strong>请检查会计科目设置是否正确</p>' : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量生成失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '生成失败：' + error.message;

        document.getElementById('batchGenerateResults').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
