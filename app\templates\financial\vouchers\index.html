{% extends "financial/base.html" %}

{% block title %}记账凭证{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- 专业财务凭证管理界面 -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0 text-dark">记账凭证</h5>
                            <small class="text-muted">Accounting Vouchers</small>
                        </div>
                        <div class="col-md-6 text-right">
                            <!-- 简洁的操作按钮 -->
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('financial.create_voucher') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 新建
                                </a>
                                <a href="{{ url_for('financial.pending_stock_ins_for_voucher') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-magic"></i> 自动生成
                                </a>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showBatchModal()">
                                    <i class="fas fa-tasks"></i> 批量
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportVouchers()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 专业的统计信息条 -->
                    <div class="bg-light border-bottom px-3 py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted d-block">总计</small>
                                <strong class="text-primary">{{ vouchers.total }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">待审核</small>
                                <strong class="text-warning">{{ pending_count or 0 }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">已审核</small>
                                <strong class="text-success">{{ approved_count or 0 }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">本月金额</small>
                                <strong class="text-info">¥{{ "{:,.2f}".format(month_amount or 0) }}</strong>
                            </div>
                        </div>
                    </div>

                    <!-- 专业的搜索筛选区 -->
                    <div class="px-3 py-2 border-bottom bg-light">
                        <form method="GET" id="searchForm">
                            <div class="row align-items-end">
                                <div class="col-md-3">
                                    <label class="form-label text-muted small mb-1">搜索</label>
                                    <input type="text" class="form-control form-control-sm" name="keyword"
                                           value="{{ keyword }}" placeholder="凭证号/摘要">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">类型</label>
                                    <select class="form-control form-control-sm" name="voucher_type">
                                        <option value="">全部</option>
                                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款凭证</option>
                                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款凭证</option>
                                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账凭证</option>
                                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账凭证</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">状态</label>
                                    <select class="form-control form-control-sm" name="status">
                                        <option value="">全部</option>
                                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">开始日期</label>
                                    <input type="date" class="form-control form-control-sm" name="start_date"
                                           value="{{ start_date }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">结束日期</label>
                                    <input type="date" class="form-control form-control-sm" name="end_date"
                                           value="{{ end_date }}">
                                </div>
                                <div class="col-md-1">
                                    <button type="submit" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 专业的凭证列表 -->
                    {% if vouchers.items %}
                    <div class="table-responsive">
                        <table class="table table-sm mb-0" id="vouchersTable">
                            <thead class="bg-light">
                                <tr>
                                    <th width="3%" class="border-0 text-center">
                                        <input type="checkbox" id="selectAll" class="form-check-input" style="margin-left: 8px;">
                                    </th>
                                    <th width="150px" class="border-0">凭证号</th>
                                    <th width="10%" class="border-0">日期</th>
                                    <th width="8%" class="border-0">类型</th>
                                    <th width="30%" class="border-0">摘要</th>
                                    <th width="12%" class="border-0 text-right">金额</th>
                                    <th width="8%" class="border-0">状态</th>
                                    <th width="15%" class="border-0 text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for voucher in vouchers.items %}
                                <tr class="border-bottom" data-voucher-id="{{ voucher.id }}">
                                    <td class="align-middle text-center">
                                        <input type="checkbox" class="voucher-checkbox form-check-input" value="{{ voucher.id }}" style="margin-left: 8px;">
                                    </td>
                                    <td class="align-middle" style="width: 150px; max-width: 150px;">
                                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}"
                                           class="text-decoration-none text-primary"
                                           style="font-family: 'Courier New', monospace; font-size: 0.9rem; font-weight: normal; word-break: break-all; white-space: normal; line-height: 1.2;">
                                            {{ voucher.voucher_number }}
                                        </a>
                                        {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                            <i class="fas fa-paperclip text-muted ml-1" title="有附件"></i>
                                        {% endif %}
                                    </td>
                                    <td class="align-middle text-muted">
                                        {{ voucher.voucher_date.strftime('%m-%d') }}
                                    </td>
                                    <td class="align-middle">
                                        <span class="badge badge-light border">{{ voucher.voucher_type }}</span>
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-truncate" style="max-width: 300px;" title="{{ voucher.summary }}">
                                            {{ voucher.summary }}
                                        </div>
                                    </td>
                                    <td class="align-middle text-right" style="font-family: 'Courier New', monospace; font-size: 0.9rem; font-weight: normal;">
                                        {{ "{:,.2f}".format(voucher.total_amount) }}
                                    </td>
                                    <td class="align-middle">
                                        {% if voucher.status == '草稿' %}
                                            <span class="badge badge-secondary">{{ voucher.status }}</span>
                                        {% elif voucher.status == '待审核' %}
                                            <span class="badge badge-warning">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已审核' %}
                                            <span class="badge badge-success">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已记账' %}
                                            <span class="badge badge-primary">{{ voucher.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <!-- 查看按钮 -->
                                            <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}"
                                               class="btn btn-outline-info btn-sm" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- 编辑按钮 -->
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}"
                                               class="btn btn-outline-warning btn-sm" title="编辑凭证">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}

                                            <!-- 文本视图按钮 -->
                                            <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}"
                                               class="btn btn-outline-secondary btn-sm" title="文本视图">
                                                <i class="fas fa-file-alt"></i>
                                            </a>

                                            <!-- 审核按钮 -->
                                            {% if voucher.status == '待审核' %}
                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                    onclick="reviewVoucher({{ voucher.id }})" title="审核通过">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}

                                            <!-- 复制按钮 -->
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    onclick="copyVoucher({{ voucher.id }})" title="复制凭证">
                                                <i class="fas fa-copy"></i>
                                            </button>

                                            <!-- 删除按钮 -->
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    onclick="deleteVoucher({{ voucher.id }})" title="删除凭证">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if vouchers.pages > 1 %}
                    <nav aria-label="凭证分页">
                        <ul class="pagination justify-content-center">
                            {% if vouchers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in vouchers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != vouchers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if vouchers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无财务凭证数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 财务凭证页面专用样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
}

/* 统计信息条样式 */
.bg-light {
    background-color: #f8f9fa !important;
}

/* 搜索区域样式 */
.form-label {
    font-weight: 500;
    color: #6c757d;
}

/* 凭证号列固定宽度 */
.voucher-number-column {
    width: 150px !important;
    max-width: 150px !important;
    min-width: 150px !important;
}

/* 表格悬停效果 */
.table-sm tbody tr:hover {
    background-color: #f8f9fa;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

.badge-light {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

/* 操作按钮样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
    margin: 0 1px;
}

.btn-group-sm .btn i {
    font-size: 0.8rem;
}

.btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-outline-success {
    color: #28a745;
    border-color: #28a745;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* 附件图标样式 */
.fa-paperclip {
    font-size: 0.75rem;
}

/* 操作按钮组紧凑显示 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.2rem;
    border-bottom-left-radius: 0.2rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.2rem;
    border-bottom-right-radius: 0.2rem;
}
</style>
{% endblock %}

{% block financial_js %}
<script>
// 页面初始化
$(document).ready(function() {
    initVoucherTable();
    bindEvents();
});

// 初始化凭证表格
function initVoucherTable() {
    // 全选功能
    $('#selectAll').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.voucher-checkbox').prop('checked', isChecked);
        updateBatchActions();
    });

    // 单个复选框
    $('.voucher-checkbox').on('change', function() {
        const total = $('.voucher-checkbox').length;
        const checked = $('.voucher-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
        updateBatchActions();
    });
}

// 绑定事件
function bindEvents() {
    // 搜索表单自动提交
    $('select[name="voucher_type"], select[name="status"]').on('change', function() {
        $('#searchForm').submit();
    });

    // 回车搜索
    $('input[name="keyword"]').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchForm').submit();
        }
    });
}

// 更新批量操作状态
function updateBatchActions() {
    const checkedCount = $('.voucher-checkbox:checked').length;
    // 这里可以显示/隐藏批量操作按钮
}

// 批量操作模态框
function showBatchModal() {
    const checkedIds = getSelectedIds();
    if (checkedIds.length === 0) {
        alert('请先选择凭证');
        return;
    }
    alert(`已选择 ${checkedIds.length} 个凭证，批量操作功能开发中...`);
}

// 导出凭证
function exportVouchers() {
    alert('导出功能开发中...');
}

// 获取选中的凭证ID
function getSelectedIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 凭证操作
function reviewVoucher(id) {
    if (confirm('确定审核此凭证？')) {
        window.location.href = `/financial/vouchers/${id}/review`;
    }
}

function copyVoucher(id) {
    alert('复制功能开发中...');
}

function deleteVoucher(id) {
    if (confirm('确定删除此凭证？此操作不可恢复！')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/financial/vouchers/${id}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    // 设置默认日期范围（本月）
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

    // 重置状态
    document.getElementById('batchGenerateProgress').style.display = 'none';
    document.getElementById('batchGenerateResults').style.display = 'none';
    document.getElementById('batchGenerateForm').style.display = 'block';
    document.getElementById('startBatchGenerate').style.display = 'inline-block';

    $('#batchGenerateModal').modal('show');
}

// 开始批量生成
document.getElementById('startBatchGenerate').addEventListener('click', function() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const autoReview = document.getElementById('autoReview').checked;

    if (!startDate || !endDate) {
        alert('请选择日期范围');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能大于结束日期');
        return;
    }

    // 隐藏表单，显示进度
    document.getElementById('batchGenerateForm').style.display = 'none';
    document.getElementById('startBatchGenerate').style.display = 'none';
    document.getElementById('batchGenerateProgress').style.display = 'block';

    // 开始批量生成
    batchGenerateVouchers(startDate, endDate, autoReview);
});

// 批量生成凭证
function batchGenerateVouchers(startDate, endDate, autoReview) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');

    progressText.textContent = '正在查找待生成凭证的入库单...';
    progressBar.style.width = '10%';
    progressBar.textContent = '10%';

    fetch('{{ url_for("financial.batch_generate_vouchers_from_stock_ins") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            auto_review: autoReview
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '生成完成！';

        // 显示结果
        document.getElementById('batchGenerateProgress').style.display = 'none';
        document.getElementById('batchGenerateResults').style.display = 'block';

        const resultsContent = document.getElementById('resultsContent');
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量生成成功</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功生成凭证：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                        <li>总金额：¥${data.total_amount.toFixed(2)}</li>
                    </ul>
                    ${data.failed_count > 0 ? '<p><strong>失败原因：</strong>请检查会计科目设置是否正确</p>' : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量生成失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '生成失败：' + error.message;

        document.getElementById('batchGenerateResults').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
