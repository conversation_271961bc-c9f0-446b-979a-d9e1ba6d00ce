2025-06-10 12:48:51,999 INFO: 应用启动 - PID: 8820 [in C:\StudentsCMSSP\app\__init__.py:826]
2025-06-10 12:50:36,428 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-10 12:50:36,454 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-10 13:11:53,070 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-10 13:11:53,095 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-10 13:12:14,079 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250610001', 42, 28, 93, 59, Decimal('68000.00'), 0, Decimal('68000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 10, 13, 12, 14), datetime.datetime(2025, 6, 10, 13, 12, 14))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-10 13:12:34,136 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-10 13:12:34,141 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
