{% extends "financial/base.html" %}

{% block title %}资产负债表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">资产负债表</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.reports_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回报表列表
                        </a>
                        <button class="btn btn-success btn-sm" onclick="exportReport()">
                            <i class="fas fa-download"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 报表参数 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="report_date">报表日期</label>
                                    <input type="date" class="form-control" id="report_date" name="report_date" 
                                           value="{{ report_date }}" onchange="this.form.submit()">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-sync"></i> 刷新报表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 资产负债表 -->
                    <div class="row">
                        <!-- 资产部分 -->
                        <div class="col-md-6">
                            <h5 class="text-center font-weight-bold">资产</h5>
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>项目</th>
                                        <th class="text-right">金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 流动资产 -->
                                    <tr class="table-info">
                                        <td><strong>流动资产：</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(assets.current_assets_total) }}</strong></td>
                                    </tr>
                                    {% for item in assets.current_assets %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                    </tr>
                                    {% endfor %}
                                    
                                    <!-- 非流动资产 -->
                                    <tr class="table-info">
                                        <td><strong>非流动资产：</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(assets.non_current_assets_total) }}</strong></td>
                                    </tr>
                                    {% for item in assets.non_current_assets %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                    </tr>
                                    {% endfor %}
                                    
                                    <!-- 资产总计 -->
                                    <tr class="table-success">
                                        <td><strong>资产总计</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(assets.total_assets) }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 负债和所有者权益部分 -->
                        <div class="col-md-6">
                            <h5 class="text-center font-weight-bold">负债和所有者权益</h5>
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>项目</th>
                                        <th class="text-right">金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 流动负债 -->
                                    <tr class="table-warning">
                                        <td><strong>流动负债：</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(liabilities.current_liabilities_total) }}</strong></td>
                                    </tr>
                                    {% for item in liabilities.current_liabilities %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                    </tr>
                                    {% endfor %}
                                    
                                    <!-- 非流动负债 -->
                                    <tr class="table-warning">
                                        <td><strong>非流动负债：</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(liabilities.non_current_liabilities_total) }}</strong></td>
                                    </tr>
                                    {% for item in liabilities.non_current_liabilities %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                    </tr>
                                    {% endfor %}
                                    
                                    <!-- 负债合计 -->
                                    <tr class="table-secondary">
                                        <td><strong>负债合计</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(liabilities.total_liabilities) }}</strong></td>
                                    </tr>
                                    
                                    <!-- 所有者权益 -->
                                    <tr class="table-info">
                                        <td><strong>所有者权益：</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(equity.total_equity) }}</strong></td>
                                    </tr>
                                    {% for item in equity.equity_items %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                    </tr>
                                    {% endfor %}
                                    
                                    <!-- 负债和所有者权益总计 -->
                                    <tr class="table-success">
                                        <td><strong>负债和所有者权益总计</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(liabilities.total_liabilities + equity.total_equity) }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 报表说明 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 报表说明</h6>
                                <ul class="mb-0">
                                    <li>报表日期：{{ report_date }}</li>
                                    <li>编制单位：{{ user_area.name }}</li>
                                    <li>金额单位：人民币元</li>
                                    <li>资产总计应等于负债和所有者权益总计</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const reportDate = document.getElementById('report_date').value;
    const url = `{{ url_for('financial.export_report', report_type='balance_sheet') }}?report_date=${reportDate}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
