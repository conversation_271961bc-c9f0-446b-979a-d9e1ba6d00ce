/*
 * CSS for beancount server pages.
 */

body {
    font-family: 'Roboto Condensed', sans-serif;
    /* font-family: 'Droid Sans Mono', sans-serif;  */
}

table {
    border-collapse: collapse;
}

table thead {
    background-color: #EEE;
}

table td {
    padding-left: 4px;
    padding-right: 4px;
}


/* The document/file title at the top. */
#title {
    padding: 5px 15px 10px 15px;
    border-bottom: medium solid #666;
    color: #666;
    font-size: larger;
}


/* All numbers everywhere. */
.num {
    text-align: right;
    font-family: 'Droid Sans Mono', sans-serif;
}



/* The navigation links. */

div.navigation {
    font-size: small;
}

div.navigation ul {
    margin-top: 5px;
    padding-left: 0px;
}

div.navigation li {
    display: inline;
    margin: 4px;
}

div.navigation li span.ledger-name {
    margin-left: 1.5em;
}

div.navigation#nav-right {
    float: right;
}


/* The title of the page, what information we're displaying. */
h1#pagetitle {
    text-align: center;
    margin-top: 0px;
}





a.account:link, a.account:visited {
  text-decoration: none;
  color: blue;
  font-weight: bold;
}


/* Tree tables */

table.tree-table {
    font-size: smaller;
    table-layout: fixed;
}

.fullwidth {
    width: 100%;
}

table.tree-table td {
    border: thin solid #eee;
}

table.tree-table th {
    color: #888;
}

/* Accounts row in tree tables. */
table.tree-table th.first {
    width: 40%;
}

table td {
    white-space: nowrap;
}

table.tree-table tr.parent-node {
    background-color: #f8f8f8;
}

table tr.totals {
    background-color: #eee;
}

span.totals-label {
    font-weight: bold;
}



/* Table of entries / Journal */

table.entry-table {
    width: 100%;
    font-size: smaller;
}

table.entry-table td {
    border: thin solid white;
}

table.trial {
    width: auto;
}

table.entry-table th {
    color: #888;
}


table.entry-table tr td.change {
    font-weight: bold;
}

table.entry-table tr td.balance {
}

table.entry-table tr td.description {
    white-space: normal;
}

table.entry-table tr td.description span.links {
    margin-left: 1em;
    margin-right: 1em;
    text-decoration: none;
}

span.payee {
    font-weight: bold;
}

span.pnsep {
    margin-left: 5px;
    margin-right: 5px;
}

table.entry-table th.description {
    width: 90%;
}


table.entry-table tr.Transaction {
}

table.entry-table tr.Transaction.warning {
    background-color: #f8a;
}

table.entry-table tr.Open {
    background-color: #e8e8e8;
}

table.entry-table tr.Close {
    background-color: #606060;
}

table.entry-table tr.Note {
    background-color: #aad0ff;
}

table.entry-table tr.Document {
    background-color: #ffc8ff;
}

table.entry-table tr.Balance {
    background-color: #cfc;
}

table.entry-table tr.Balance.fail {
    background-color: #f8a;
}

table.entry-table tr.Pad {
    background-color: #8ff;
}

table.entry-table tr.Padding, table.entry-table tr.Summarize, table.entry-table tr.Transfer {
    background-color: #cff;
}

table.entry-table tr.Posting {
    font-size: x-small;
    background-color: #e8e8e8;
    opacity: 0.6;
}

table.entry-table tr.Posting.warning {
    background-color: #f66;
}



/* Side-by-side views */

div.halfleft h3, div.halfright h3 {
    text-align: center;
    margin: 10px 0px 10px 0px;
}

div.halfleft {
    float: left;
    left: 1%;
    width: 50%;
    margin: 0px;
    padding: 0px;
}

div.halfright {
    float: right;
    left: 50%;
    width: 49%;
    margin: 0px;
    padding: 0px;
}

/* For balance sheet, add a little more spacing between liabilities and equity. */
div.spacer {
    height: 14px;
}



/* Holdings tables. */

table.holdings {
    border-collapse: collapse;
}

table.holdings th, table.holdings td {
    border: thin solid black;
    padding-left: 0.5em;
    padding-right: 0.5em;
}

table.holdings tbody td {
    font-family: 'Droid Sans Mono', sans-serif;
    text-align: right;
}

table.holdings.detail-table td:nth-child(1) {
    font-family: 'Roboto Condensed', sans-serif;
    text-align: left;
}





div#viewboxes {
}

div#viewboxes hr {
    margin-left:20px;
    margin-right:20px;
}

div.viewbox {
    padding: 0px;
}

div.viewbox ul {
    padding: 3px;
    margin: 0px;
}

div.viewbox h2 {
    margin-top: 5px;
    margin-bottom: 0px;
    display: inline-block;
}

div.viewbox ul li {
    display: inline-block;
    white-space: nowrap;
    margin: 2px;
    padding-left: 5px;
    padding-right: 5px;
}

div.viewbox#year ul li {
    min-width: 80px;
}

div.viewbox ul li {
    min-width: 200px;
}




table#price-index {
    margin-left: auto;
    margin-right: auto;
}

table#price-index td {
    vertical-align: top;
}


/* Source code rendering */
div#source pre {
   margin-top: 0px;
   margin-bottom: 0px;
}


/* Overlay */
div#overlay {
    left: 3%;
    right: 3%;
    padding: 1em;
    margin-left: auto;
    margin-right: auto;
    position: absolute;
    background-color: #FEE;
    opacity: 0.95;
    border: 3px solid black;
    border-radius: 10px;
}


/* Entry context */

div.filelink {
}
