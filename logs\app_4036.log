2025-06-08 19:57:54,198 INFO: 应用启动 - PID: 4036 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:823]
2025-06-08 20:02:14,482 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-08 20:02:14,496 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-08 20:03:45,813 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-08 20:03:45,813 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-08 20:03:45,815 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-08 20:03:45,816 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-08 20:03:53,725 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-08 20:03:53,733 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-08 20:04:29,042 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:04:29,043 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:05:02,694 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:05:02,695 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as period_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as period_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = ?
                AND vd.subject_id = ?
                AND fv.voucher_date >= ?
                AND fv.voucher_date <= ?
                AND fv.status IN ('已审核', '已记账')
            ]
[parameters: (42, 85, datetime.date(2025, 6, 1), datetime.date(2025, 6, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:354]
2025-06-08 20:05:11,397 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:05:11,398 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as period_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as period_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = ?
                AND vd.subject_id = ?
                AND fv.voucher_date >= ?
                AND fv.voucher_date <= ?
                AND fv.status IN ('已审核', '已记账')
            ]
[parameters: (42, 85, datetime.date(2025, 6, 1), datetime.date(2025, 6, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:354]
2025-06-08 20:07:07,955 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:07,955 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:07:13,336 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 86, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:13,337 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 86, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:07:16,416 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 87, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:16,417 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 87, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:07:20,848 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 88, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:20,849 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 88, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:07:25,981 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 89, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:25,982 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 89, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:07:33,365 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 90, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:33,365 ERROR: 获取科目明细记录失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date >= ?
            AND fv.voucher_date <= ?
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        ]
[parameters: (42, 90, datetime.date(2025, 6, 1), datetime.date(2025, 6, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:286]
2025-06-08 20:07:42,139 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:42,141 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as period_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as period_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = ?
                AND vd.subject_id = ?
                AND fv.voucher_date >= ?
                AND fv.voucher_date <= ?
                AND fv.status IN ('已审核', '已记账')
            ]
[parameters: (42, 85, datetime.date(2025, 6, 1), datetime.date(2025, 6, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:354]
2025-06-08 20:07:51,955 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 85, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:07:51,956 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as period_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as period_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = ?
                AND vd.subject_id = ?
                AND fv.voucher_date >= ?
                AND fv.voucher_date <= ?
                AND fv.status IN ('已审核', '已记账')
            ]
[parameters: (42, 85, datetime.date(2025, 6, 1), datetime.date(2025, 6, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:354]
2025-06-08 20:08:02,912 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 91, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:08:02,913 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as period_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as period_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = ?
                AND vd.subject_id = ?
                AND fv.voucher_date >= ?
                AND fv.voucher_date <= ?
                AND fv.status IN ('已审核', '已记账')
            ]
[parameters: (42, 91, datetime.date(2025, 6, 1), datetime.date(2025, 6, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:354]
2025-06-08 20:08:09,793 ERROR: 计算期初余额失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = ?
            AND vd.subject_id = ?
            AND fv.voucher_date < ?
            AND fv.status IN ('已审核', '已记账')
        ]
[parameters: (42, 93, datetime.date(2025, 6, 1))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:228]
2025-06-08 20:08:09,794 ERROR: 获取总账汇总数据失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as period_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as period_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = ?
                AND vd.subject_id = ?
                AND fv.voucher_date >= ?
                AND fv.voucher_date <= ?
                AND fv.status IN ('已审核', '已记账')
            ]
[parameters: (42, 93, datetime.date(2025, 6, 1), datetime.date(2025, 6, 8))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\ledgers.py:354]
