#!/usr/bin/env python3
"""
测试路由注册情况
"""

from app import create_app

def test_routes():
    app = create_app()
    
    print("=== 财务模块路由列表 ===")
    
    with app.app_context():
        # 获取所有路由
        for rule in app.url_map.iter_rules():
            if 'financial' in rule.rule:
                print(f"路由: {rule.rule}")
                print(f"方法: {list(rule.methods)}")
                print(f"端点: {rule.endpoint}")
                print("---")
    
    print("\n=== 查找专业编辑器路由 ===")
    
    with app.app_context():
        # 查找专业编辑器路由
        found_pro_route = False
        for rule in app.url_map.iter_rules():
            if 'edit-pro' in rule.rule:
                print(f"✓ 找到专业编辑器路由: {rule.rule}")
                print(f"  方法: {list(rule.methods)}")
                print(f"  端点: {rule.endpoint}")
                found_pro_route = True
        
        if not found_pro_route:
            print("✗ 未找到专业编辑器路由")
            
            # 查找所有编辑相关路由
            print("\n=== 所有编辑相关路由 ===")
            for rule in app.url_map.iter_rules():
                if 'edit' in rule.rule and 'financial' in rule.rule:
                    print(f"路由: {rule.rule}")
                    print(f"端点: {rule.endpoint}")

if __name__ == '__main__':
    test_routes()
