{% extends "_layout.html" %}

{% import 'macros/_account_macros.html' as account_macros %}
{% import 'macros/_commodity_macros.html' as commodity_macros %}

{% set page_title = _('Statistics') %}

{% block content %}
<div class="left">
  {% set postings_per_account = 'SELECT account, count(account) ORDER BY account' %}
  <h3>
    {{ _('Postings per Account') }}
    (<a href="{{ url_for('report', report_name='query', query_string=postings_per_account) }}">Query</a>)
  </h3>
  <svelte-component type="query-table"><script type="application/json">{{ledger.query_shell.execute_query_serialised(g.filtered.entries, postings_per_account)|tojson}}</script></svelte-component>
</div>

{% set status_sortorder = { 'red': 5, 'yellow': 4, 'green': 3, '': 2 } %}
<div class="left">
  <h3>{{ _('Update Activity') }}<copyable-text class="button right" title="
  {{- _('Click to copy balance directives for accounts (except green ones) to the clipboard.') -}}
  " data-clipboard-text="{{ ledger.accounts.all_balance_directives() }}">{{ _('Copy balance directives') }}</copyable-text></h3>
  <table is="sortable-table">
    <thead>
      <tr>
        <th data-sort="string" data-order="asc">{{ _('Account') }}</th>
        <th class="indicator-header" data-sort="num"></th>
        <th class="indicator-header" data-sort="num"></th>
        <th data-sort="string">{{ _('Last Entry') }}</th>
        <th>{{ _('Balance') }}</th>
      </tr>
    </thead>
    <tbody>
      {% for account in ledger.attributes.accounts %}
      {% if account.startswith(ledger.options['name_assets']) or account.startswith(ledger.options['name_liabilities']) %}
      {% set last_entry = ledger.accounts[account].last_entry %}
      {% if last_entry %}
      <tr>
        <td class="account"><a href="{{ url_for('account', name=account) }}">{{ account }}</a></td>
        {% if ledger.accounts[account].uptodate_status %}
        <td data-sort-value="{{ status_sortorder[ledger.accounts[account].uptodate_status] }}">{{ account_macros.indicator(ledger, account) }}</td>
        <td>{{ account_macros.last_account_activity(ledger, account) }}</td>
        {% else %}
        <td data-sort-value="0"></td>
        <td></td>
        {% endif %}
        <td><a href="#context-{{ last_entry.entry_hash }}">{{ last_entry.date }}</a></td>
        <td class="num">
          {%- for currency, number in ((g.filtered.root_tree.get(account)).balance|units).items() -%}
          {{ commodity_macros.render_num(ledger, currency, number) }}<br>
          {% endfor -%}
        </td>
      </tr>
      {% endif %}
      {% endif %}
      {% endfor %}
    </tbody>
  </table>
</div>

<div class="left">
  <h3>{{ _('Entries per Type') }}</h3>
  <table is="sortable-table">
    <thead>
      <tr>
        <th data-sort="string">{{ _('Type') }}</th>
        <th data-sort="num">{{ _('# Entries') }}</th>
      </tr>
    </thead>
    <tbody>
      {% for type, entries in ledger.group_entries_by_type(g.filtered.entries)._asdict().items() %}
      <tr>
        <td>{{ type }}</td>
        <td class="num">{{ entries|length }}</td>
      </tr>
      {% endfor %}
    </tbody>
    <tfoot>
      <tr>
        <td>{{ _('Total') }}</td>
        <td class="num">{{ g.filtered.entries|length }}</td>
      </tr>
    </tfoot>
  </table>
</div>
{% endblock %}
