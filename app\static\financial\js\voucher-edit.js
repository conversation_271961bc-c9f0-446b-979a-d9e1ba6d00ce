/**
 * 财务凭证编辑页面专用JavaScript
 */

// 金额转中文大写函数
function numToCny(num) {
    if (num === 0 || num === '0' || num === '') return '零元整';
    
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟'];
    const bigUnits = ['', '万', '亿', '兆'];
    
    // 转换为分（避免浮点数精度问题）
    let numStr = Math.round(parseFloat(num) * 100).toString();
    
    if (numStr === '0') return '零元整';
    
    // 分离整数部分和小数部分
    let integerPart = numStr.slice(0, -2) || '0';
    let decimalPart = numStr.slice(-2);
    
    let result = '';
    
    // 处理整数部分
    if (parseInt(integerPart) === 0) {
        result = '零';
    } else {
        // 按四位分组
        let groups = [];
        while (integerPart.length > 0) {
            let group = integerPart.slice(-4);
            integerPart = integerPart.slice(0, -4);
            groups.unshift(group);
        }
        
        for (let i = 0; i < groups.length; i++) {
            let group = groups[i];
            let groupResult = convertGroup(group, digits, units);
            
            if (groupResult) {
                result += groupResult + bigUnits[groups.length - 1 - i];
            }
        }
    }
    
    result += '元';
    
    // 处理小数部分
    let jiao = parseInt(decimalPart[0]);
    let fen = parseInt(decimalPart[1]);
    
    if (jiao === 0 && fen === 0) {
        result += '整';
    } else {
        if (jiao !== 0) {
            result += digits[jiao] + '角';
        }
        if (fen !== 0) {
            result += digits[fen] + '分';
        }
    }
    
    return result;
}

// 转换四位数组
function convertGroup(group, digits, units) {
    let result = '';
    let hasZero = false;
    
    for (let i = 0; i < group.length; i++) {
        let digit = parseInt(group[i]);
        let unitIndex = group.length - 1 - i;
        
        if (digit !== 0) {
            if (hasZero) {
                result += '零';
                hasZero = false;
            }
            result += digits[digit] + units[unitIndex];
        } else if (result && i < group.length - 1) {
            hasZero = true;
        }
    }
    
    return result;
}

// 格式化金额显示（10位格式：千万到分）
function formatAmount(amount) {
    if (!amount || amount === '') return '0000000000';
    const num = parseFloat(amount);
    if (isNaN(num)) return '0000000000';

    // 转换为分（乘以100），然后格式化为10位字符串
    const cents = Math.round(num * 100);
    return cents.toString().padStart(10, '0');
}

// 从10位格式转换为普通金额
function parseAmountFromFormat(formattedAmount) {
    if (!formattedAmount || formattedAmount === '0000000000') return 0;
    const cents = parseInt(formattedAmount) || 0;
    return cents / 100;
}

// 验证金额输入（10位格式）
function validateAmount(value) {
    if (!value || value === '') return true;

    // 只允许数字
    if (!/^\d*$/.test(value)) return false;

    // 检查长度不超过10位
    if (value.length > 10) return false;

    // 检查金额范围（最大99999999.99元，即9999999999分）
    const num = parseInt(value) || 0;
    if (num < 0 || num > 9999999999) return false;

    return true;
}

// 格式化金额输入（确保10位）
function formatAmountInput(input) {
    let value = input.value.replace(/\D/g, ''); // 只保留数字
    if (value.length > 10) {
        value = value.slice(0, 10);
    }
    input.value = value.padStart(10, '0');
}

// 凭证编辑器类
class VoucherEditor {
    constructor(voucherId) {
        this.voucherId = voucherId;
        this.currentEditingRow = null;
        this.currentSubjectInput = null;
        this.allSubjects = [];
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initializeAmountInputs();
        this.updateTotals();
        console.log('凭证编辑器初始化完成');

        // 测试打印功能
        console.log('测试打印功能绑定...');
        const printBtn = document.getElementById('print-voucher-btn');
        if (printBtn) {
            console.log('找到打印按钮');
        } else {
            console.log('未找到打印按钮');
        }
    }

    initializeAmountInputs() {
        // 初始化现有的金额输入框
        document.querySelectorAll('.amount-input').forEach(input => {
            if (input.value && input.value !== '0000000000' && input.value !== '') {
                // 如果有值，确保格式正确
                const amount = parseFloat(input.value) || 0;
                input.value = formatAmount(amount);
            } else {
                input.value = '0000000000';
            }
        });
    }
    
    bindEvents() {
        // 添加分录按钮
        const addBtn = document.getElementById('add-row-btn');
        if (addBtn) {
            console.log('绑定添加分录按钮事件');
            addBtn.addEventListener('click', () => {
                console.log('添加分录按钮被点击');
                this.addRow();
            });
        } else {
            console.log('未找到添加分录按钮');
        }
        
        // 保存凭证按钮
        const saveBtn = document.getElementById('save-voucher-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveVoucher());
        }

        // 提交审核按钮
        const submitBtn = document.getElementById('submit-review-btn');
        if (submitBtn) {
            submitBtn.addEventListener('click', () => this.submitForReview());
        }

        // 检查平衡按钮
        const balanceBtn = document.getElementById('check-balance-btn');
        if (balanceBtn) {
            balanceBtn.addEventListener('click', () => this.checkBalance());
        }

        // 打印凭证按钮
        const printBtn = document.getElementById('print-voucher-btn');
        if (printBtn) {
            console.log('绑定打印按钮事件');
            printBtn.addEventListener('click', () => {
                console.log('打印按钮被点击');
                this.printVoucher();
            });
        } else {
            console.log('未找到打印按钮');
        }
        
        // 事件委托
        document.addEventListener('click', (e) => this.handleClick(e));
        document.addEventListener('input', (e) => this.handleInput(e));
        document.addEventListener('blur', (e) => this.handleBlur(e));
        document.addEventListener('focus', (e) => this.handleFocus(e));
        
        // 会计科目选择确认
        const confirmBtn = document.getElementById('confirm-subject-btn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.confirmSubjectSelection());
        }
    }
    
    handleClick(e) {
        if (e.target.closest('.edit-row-btn')) {
            this.editRow(e.target.closest('tr'));
        } else if (e.target.closest('.delete-row-btn')) {
            const detailId = e.target.closest('.delete-row-btn').dataset.detailId;
            this.deleteRow(e.target.closest('tr'), detailId);
        } else if (e.target.closest('.save-row-btn')) {
            this.saveRow(e.target.closest('tr'));
        } else if (e.target.closest('.cancel-row-btn')) {
            this.cancelRow(e.target.closest('tr'));
        } else if (e.target.closest('.subject-input')) {
            console.log('科目输入框被点击');
            this.openSubjectSelector(e.target);
        }
    }
    
    handleInput(e) {
        if (e.target.classList.contains('amount-input')) {
            // 验证金额输入
            if (!validateAmount(e.target.value)) {
                e.target.value = e.target.value.slice(0, -1);
                return;
            }

            // 借贷金额互斥
            const row = e.target.closest('tr');
            if (e.target.classList.contains('debit-input') && e.target.value && e.target.value !== '0000000000') {
                row.querySelector('.credit-input').value = '0000000000';
            } else if (e.target.classList.contains('credit-input') && e.target.value && e.target.value !== '0000000000') {
                row.querySelector('.debit-input').value = '0000000000';
            }

            this.updateTotals();
        }
    }

    handleBlur(e) {
        if (e.target.classList.contains('amount-input')) {
            // 失去焦点时格式化金额
            formatAmountInput(e.target);
            this.updateTotals();
        }
    }

    handleFocus(e) {
        if (e.target.classList.contains('amount-input')) {
            // 获得焦点时，如果是默认值则清空
            if (e.target.value === '0000000000') {
                e.target.value = '';
            }
        }
    }

    updateTotals() {
        let debitTotal = 0;
        let creditTotal = 0;

        // 计算借贷方合计
        document.querySelectorAll('.amount-input').forEach(input => {
            const amount = parseAmountFromFormat(input.value);
            if (input.classList.contains('debit-input')) {
                debitTotal += amount;
            } else if (input.classList.contains('credit-input')) {
                creditTotal += amount;
            }
        });

        // 更新合计显示
        document.getElementById('debit-total').textContent = formatAmount(debitTotal);
        document.getElementById('credit-total').textContent = formatAmount(creditTotal);

        // 更新金额大写
        document.getElementById('amount-in-words').textContent = `合计大写：${numToCny(debitTotal)}`;
    }

    addRow() {
        console.log('addRow方法被调用');
        const tbody = document.getElementById('voucher-rows');
        if (!tbody) {
            console.error('未找到voucher-rows元素');
            return;
        }
        console.log('找到tbody元素，准备添加新行');
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td>
                <input type="text" class="form-control summary-input" value="">
            </td>
            <td>
                <input type="text" class="form-control subject-input" value="" readonly placeholder="点击选择科目">
                <input type="hidden" class="subject-id" value="">
            </td>
            <td>
                <input type="text" class="form-control debit-input amount-input" value="0000000000">
            </td>
            <td>
                <input type="text" class="form-control credit-input amount-input" value="0000000000">
            </td>
            <td>
                <div class="btn-group-sm">
                    <button type="button" class="btn btn-sm btn-success save-row-btn">
                        <i class="fas fa-save"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary cancel-row-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(newRow);
        this.editRow(newRow);
    }

    editRow(row) {
        if (this.currentEditingRow) {
            this.cancelRow(this.currentEditingRow);
        }

        this.currentEditingRow = row;
        const inputs = row.querySelectorAll('input');
        inputs.forEach(input => {
            input.readOnly = false;
            if (input.classList.contains('amount-input')) {
                input.value = input.value === '0000000000' ? '' : input.value;
            }
        });

        // 更新按钮状态
        const btnGroup = row.querySelector('.btn-group-sm');
        btnGroup.innerHTML = `
            <button type="button" class="btn btn-sm btn-success save-row-btn">
                <i class="fas fa-save"></i>
            </button>
            <button type="button" class="btn btn-sm btn-secondary cancel-row-btn">
                <i class="fas fa-times"></i>
            </button>
        `;
    }

    saveRow(row) {
        const summaryInput = row.querySelector('.summary-input');
        const subjectIdInput = row.querySelector('.subject-id');
        const debitInput = row.querySelector('.debit-input');
        const creditInput = row.querySelector('.credit-input');

        if (!this.validateRowData(summaryInput, subjectIdInput, debitInput, creditInput)) {
            return;
        }

        const data = {
            summary: summaryInput.value,
            subject_id: subjectIdInput.value,
            debit_amount: parseAmountFromFormat(debitInput.value),
            credit_amount: parseAmountFromFormat(creditInput.value)
        };

        this.submitRowData(row, data);
    }

    validateRowData(summaryInput, subjectIdInput, debitInput, creditInput) {
        if (!summaryInput.value.trim()) {
            this.showToast('error', '请输入摘要');
            summaryInput.focus();
            return false;
        }

        if (!subjectIdInput.value) {
            this.showToast('error', '请选择会计科目');
            return false;
        }

        const debitAmount = parseAmountFromFormat(debitInput.value);
        const creditAmount = parseAmountFromFormat(creditInput.value);

        if (debitAmount === 0 && creditAmount === 0) {
            this.showToast('error', '请输入金额');
            return false;
        }

        if (debitAmount > 0 && creditAmount > 0) {
            this.showToast('error', '借贷方不能同时有金额');
            return false;
        }

        return true;
    }

    submitRowData(row, data) {
        const detailId = row.dataset.detailId;
        const url = detailId ? 
            `/financial/vouchers/${this.voucherId}/details/${detailId}` :
            `/financial/vouchers/${this.voucherId}/details`;
        
        const method = detailId ? 'PUT' : 'POST';

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                this.onRowSaveSuccess(row, result.detail_id, result);
            } else {
                this.showToast('error', result.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存分录失败:', error);
            this.showToast('error', '保存失败，请重试');
        });
    }

    onRowSaveSuccess(row, detailId, result) {
        // 更新行数据
        row.dataset.detailId = detailId;
        row.querySelector('.summary-input').readOnly = true;
        row.querySelector('.subject-input').readOnly = true;
        row.querySelector('.debit-input').readOnly = true;
        row.querySelector('.credit-input').readOnly = true;

        // 更新按钮状态
        const btnGroup = row.querySelector('.btn-group-sm');
        btnGroup.innerHTML = `
            <button type="button" class="btn btn-sm btn-warning edit-row-btn" data-detail-id="${detailId}">
                <i class="fas fa-edit"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger delete-row-btn" data-detail-id="${detailId}">
                <i class="fas fa-trash"></i>
            </button>
        `;

        this.currentEditingRow = null;
        this.updateTotals();
        this.showToast('success', '保存成功');
    }

    cancelRow(row) {
        if (!row.dataset.detailId) {
            // 如果是新行，直接删除
            row.remove();
        } else {
            // 恢复原始数据
            const inputs = row.querySelectorAll('input');
            inputs.forEach(input => {
                input.readOnly = true;
                if (input.classList.contains('amount-input')) {
                    input.value = input.value === '' ? '0000000000' : input.value;
                }
            });

            // 恢复按钮状态
            const btnGroup = row.querySelector('.btn-group-sm');
            btnGroup.innerHTML = `
                <button type="button" class="btn btn-sm btn-warning edit-row-btn" data-detail-id="${row.dataset.detailId}">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger delete-row-btn" data-detail-id="${row.dataset.detailId}">
                    <i class="fas fa-trash"></i>
                </button>
            `;
        }

        this.currentEditingRow = null;
        this.updateTotals();
    }

    deleteRow(row, detailId) {
        if (!confirm('确定要删除此分录吗？')) {
            return;
        }

        fetch(`/financial/vouchers/${this.voucherId}/details/${detailId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                row.remove();
                this.updateTotals();
                this.showToast('success', '删除成功');
            } else {
                this.showToast('error', result.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('删除分录失败:', error);
            this.showToast('error', '删除失败，请重试');
        });
    }

    openSubjectSelector(input) {
        console.log('openSubjectSelector被调用');
        this.currentSubjectInput = input;

        // 检查jQuery和Bootstrap是否可用
        if (typeof $ === 'undefined') {
            console.error('jQuery未加载');
            alert('系统错误：jQuery未加载');
            return;
        }

        console.log('显示科目选择弹窗');
        $('#subject-tree-modal').modal('show');
        this.loadSubjects();
    }

    loadSubjects() {
        fetch('/financial/accounting-subjects/api?include_system=true')
            .then(response => response.json())
            .then(data => {
                this.allSubjects = data;
                this.renderSubjectTree();
            })
            .catch(error => {
                console.error('加载科目失败:', error);
                this.showToast('error', '加载科目失败，请重试');
            });
    }

    renderSubjectTree() {
        const container = document.getElementById('subject-tree-container');
        if (!container) return;

        // 添加搜索框
        let html = `
            <div class="mb-3">
                <input type="text" class="form-control" id="subject-search" placeholder="搜索科目编码或名称...">
            </div>
        `;

        // 按科目类型分组
        const groupedSubjects = {};
        this.allSubjects.forEach(subject => {
            if (!groupedSubjects[subject.subject_type]) {
                groupedSubjects[subject.subject_type] = [];
            }
            groupedSubjects[subject.subject_type].push(subject);
        });

        const typeOrder = ['资产', '负债', '净资产', '收入', '费用'];

        typeOrder.forEach(type => {
            if (groupedSubjects[type] && groupedSubjects[type].length > 0) {
                const groupId = `subject-group-${type}`;
                html += `
                    <div class="mb-3 subject-group" id="${groupId}">
                        <h6 class="text-primary border-bottom pb-2 subject-group-title" 
                            style="cursor:pointer;user-select:none;" 
                            data-group="${groupId}">
                            <i class="fas fa-folder-open mr-1 group-toggle-icon"></i> 
                            ${type}类科目
                        </h6>
                        <div class="subject-group-content">
                `;

                const topLevelSubjects = groupedSubjects[type].filter(s => s.level === 1);
                topLevelSubjects.forEach(subject => {
                    html += this.buildSubjectItem(subject, groupedSubjects[type]);
                });

                html += `
                        </div>
                    </div>
                `;
            }
        });

        container.innerHTML = html;

        // 绑定搜索事件
        const searchInput = document.getElementById('subject-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterSubjects(e.target.value);
            });
        }

        // 绑定分组折叠/展开事件
        document.querySelectorAll('.subject-group-title').forEach(title => {
            title.addEventListener('click', () => {
                const groupId = title.dataset.group;
                const groupDiv = document.getElementById(groupId);
                const content = groupDiv.querySelector('.subject-group-content');
                const icon = title.querySelector('.group-toggle-icon');

                if (content.style.display === 'none') {
                    content.style.display = '';
                    icon.className = 'fas fa-folder-open mr-1 group-toggle-icon';
                } else {
                    content.style.display = 'none';
                    icon.className = 'fas fa-folder mr-1 group-toggle-icon';
                }
            });
        });

        // 绑定科目选择事件
        container.addEventListener('click', (e) => {
            const subjectItem = e.target.closest('.subject-item .d-flex');
            if (subjectItem && !e.target.closest('.expand-icon')) {
                // 清除之前的选择
                container.querySelectorAll('.subject-item .d-flex').forEach(item => {
                    item.classList.remove('bg-primary', 'text-white', 'selected');
                });

                // 选中当前项
                subjectItem.classList.add('bg-primary', 'text-white', 'selected');
                subjectItem.closest('.subject-item').classList.add('selected');

                // 启用确认按钮
                document.getElementById('confirm-subject-btn').disabled = false;
            }
        });

        // 绑定展开/收起事件
        container.addEventListener('click', (e) => {
            const expandIcon = e.target.closest('.expand-icon');
            if (expandIcon) {
                const subjectItem = expandIcon.closest('.subject-item');
                const children = subjectItem.querySelector('.children');
                const icon = expandIcon.querySelector('i');

                if (children && children.children.length > 0) {
                    if (children.style.display === 'none') {
                        children.style.display = '';
                        icon.className = 'fas fa-chevron-down';
                    } else {
                        children.style.display = 'none';
                        icon.className = 'fas fa-chevron-right';
                    }
                }
            }
        });

        // 默认全部收起
        document.querySelectorAll('.subject-group-content').forEach(content => {
            content.style.display = 'none';
        });
    }

    buildSubjectItem(subject, allTypeSubjects) {
        const children = allTypeSubjects.filter(s => s.parent_id === subject.id);
        const hasChildren = children.length > 0;

        let html = `
            <div class="subject-item" data-id="${subject.id}">
                <div class="d-flex align-items-center p-2 mb-1 border rounded hover-bg-light"
                     style="cursor: pointer; margin-left: ${(subject.level - 1) * 20}px;">
                    <span class="expand-icon mr-2 d-flex align-items-center justify-content-center"
                          style="width: 16px; height: 16px;">
                        ${hasChildren ? '<i class="fas fa-chevron-right"></i>' : '<i class="fas fa-circle" style="font-size: 6px;"></i>'}
                    </span>
                    <code class="mr-2 text-dark" style="background: none; font-size: 0.9em;">${subject.code}</code>
                    <span class="flex-grow-1">${subject.name}</span>
                </div>
                <div class="children" style="display: none;">
        `;

        children.forEach(child => {
            html += this.buildSubjectItem(child, allTypeSubjects);
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    filterSubjects(keyword) {
        const groups = document.querySelectorAll('.subject-group');

        if (!keyword.trim()) {
            // 显示所有项目
            groups.forEach(group => {
                group.style.display = 'block';
                const items = group.querySelectorAll('.subject-item');
                items.forEach(item => item.style.display = 'block');
            });
            return;
        }

        keyword = keyword.toLowerCase();

        groups.forEach(group => {
            const items = group.querySelectorAll('.subject-item');
            let hasVisibleItems = false;

            items.forEach(item => {
                const subjectDiv = item.querySelector('.d-flex');
                if (subjectDiv) {
                    const code = subjectDiv.querySelector('code').textContent.toLowerCase();
                    const name = subjectDiv.querySelector('span:last-child').textContent.toLowerCase();

                    if (code.includes(keyword) || name.includes(keyword)) {
                        item.style.display = 'block';
                        hasVisibleItems = true;

                        // 展开父级分组
                        const groupContent = item.closest('.subject-group-content');
                        if (groupContent) {
                            groupContent.style.display = '';
                            const icon = group.querySelector('.group-toggle-icon');
                            if (icon) {
                                icon.className = 'fas fa-folder-open mr-1 group-toggle-icon';
                            }
                        }
                    } else {
                        item.style.display = 'none';
                    }
                }
            });

            group.style.display = hasVisibleItems ? 'block' : 'none';
        });
    }

    confirmSubjectSelection() {
        const selectedItem = document.querySelector('.subject-item.selected');
        if (!selectedItem) {
            this.showToast('error', '请选择会计科目');
            return;
        }

        const subjectId = selectedItem.dataset.id;
        const subject = this.allSubjects.find(s => s.id == subjectId);
        if (!subject) {
            this.showToast('error', '科目数据无效');
            return;
        }

        // 更新输入框
        this.currentSubjectInput.value = `${subject.code} - ${subject.name}`;
        this.currentSubjectInput.closest('td').querySelector('.subject-id').value = subjectId;

        // 关闭弹窗
        $('#subject-tree-modal').modal('hide');
    }

    saveVoucher() {
        if (!this.checkBalance(false)) {
            return;
        }

        const data = {
            voucher_type: document.getElementById('voucherType').value,
            voucher_date: document.getElementById('voucherDate').value,
            attachment_count: document.getElementById('attachmentCount').value
        };

        fetch(`/financial/vouchers/${this.voucherId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                this.showToast('success', '保存成功');
                setTimeout(() => {
                    window.location.href = `/financial/vouchers/${this.voucherId}`;
                }, 1500);
            } else {
                this.showToast('error', result.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存凭证失败:', error);
            this.showToast('error', '保存失败，请重试');
        });
    }

    submitForReview() {
        if (!this.checkBalance(false)) {
            return;
        }

        if (!confirm('确定要提交审核吗？提交后将不能修改。')) {
            return;
        }

        fetch(`/financial/vouchers/${this.voucherId}/submit-review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                this.showToast('success', '提交成功');
                setTimeout(() => {
                    window.location.href = `/financial/vouchers/${this.voucherId}`;
                }, 1500);
            } else {
                this.showToast('error', result.message || '提交失败');
            }
        })
        .catch(error => {
            console.error('提交审核失败:', error);
            this.showToast('error', '提交失败，请重试');
        });
    }

    checkBalance(showMessage = true) {
        const debitTotal = parseAmountFromFormat(document.getElementById('debit-total').textContent);
        const creditTotal = parseAmountFromFormat(document.getElementById('credit-total').textContent);

        if (debitTotal === 0 && creditTotal === 0) {
            if (showMessage) {
                this.showToast('error', '凭证金额不能为零');
            }
            return false;
        }

        if (Math.abs(debitTotal - creditTotal) > 0.01) {
            if (showMessage) {
                this.showToast('error', '借贷不平衡');
            }
            return false;
        }

        if (showMessage) {
            this.showToast('success', '借贷平衡');
        }
        return true;
    }

    printVoucher() {
        console.log('打印功能被调用');
        alert('打印功能测试 - 这里应该调用打印对话框');

        // 简单的打印功能
        try {
            window.print();
        } catch (error) {
            console.error('打印失败:', error);
            alert('打印失败: ' + error.message);
        }
    }

    showToast(type, message) {
        // 使用 Bootstrap 的 Toast 组件
        const toastContainer = document.getElementById('toast-container') || (() => {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        })();

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 3000
        });
        bsToast.show();

        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// 导出给全局使用
window.VoucherEditor = VoucherEditor;
window.numToCny = numToCny;
window.formatAmount = formatAmount;
window.parseAmountFromFormat = parseAmountFromFormat;
window.validateAmount = validateAmount;
window.formatAmountInput = formatAmountInput;
