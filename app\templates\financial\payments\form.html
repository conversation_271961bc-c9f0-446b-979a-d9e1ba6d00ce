{% extends "financial/base.html" %}

{% block title %}创建付款记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建付款记录</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.payments_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.payment_date.label(class="form-label") }}
                                    {{ form.payment_date(class="form-control") }}
                                    {% if form.payment_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.payment_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.payment_method.label(class="form-label") }}
                                    {{ form.payment_method(class="form-control") }}
                                    {% if form.payment_method.errors %}
                                        <div class="text-danger">
                                            {% for error in form.payment_method.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.payable_id.label(class="form-label") }}
                                    {{ form.payable_id(class="form-control", id="payable_select") }}
                                    {% if form.payable_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.payable_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.amount.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.amount(class="form-control", id="amount_input") }}
                                        <div class="input-group-append">
                                            <span class="input-group-text">元</span>
                                        </div>
                                    </div>
                                    {% if form.amount.errors %}
                                        <div class="text-danger">
                                            {% for error in form.amount.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted" id="balance_info"></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.bank_account.label(class="form-label") }}
                                    {{ form.bank_account(class="form-control") }}
                                    {% if form.bank_account.errors %}
                                        <div class="text-danger">
                                            {% for error in form.bank_account.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.reference_number.label(class="form-label") }}
                                    {{ form.reference_number(class="form-control") }}
                                    {% if form.reference_number.errors %}
                                        <div class="text-danger">
                                            {% for error in form.reference_number.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            {% if form.summary.errors %}
                                <div class="text-danger">
                                    {% for error in form.summary.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 创建付款记录
                            </button>
                            <a href="{{ url_for('financial.payments_index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const payableSelect = document.getElementById('payable_select');
    const amountInput = document.getElementById('amount_input');
    const balanceInfo = document.getElementById('balance_info');
    
    // 当选择应付账款时，显示余额信息
    payableSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value && selectedOption.value !== '0') {
            const text = selectedOption.text;
            const balanceMatch = text.match(/¥([\d,]+\.?\d*)/);
            if (balanceMatch) {
                const balance = balanceMatch[1].replace(/,/g, '');
                balanceInfo.textContent = `应付账款余额：¥${balance}`;
                balanceInfo.className = 'form-text text-info';
                
                // 设置最大付款金额
                amountInput.setAttribute('max', balance);
            }
        } else {
            balanceInfo.textContent = '';
            amountInput.removeAttribute('max');
        }
    });
    
    // 验证付款金额
    amountInput.addEventListener('input', function() {
        const maxAmount = parseFloat(this.getAttribute('max'));
        const currentAmount = parseFloat(this.value);
        
        if (maxAmount && currentAmount > maxAmount) {
            balanceInfo.textContent = `付款金额不能超过应付账款余额 ¥${maxAmount}`;
            balanceInfo.className = 'form-text text-danger';
        } else if (maxAmount) {
            balanceInfo.textContent = `应付账款余额：¥${maxAmount}`;
            balanceInfo.className = 'form-text text-info';
        }
    });
    
    // 如果URL中有payable_id参数，自动选择对应的应付账款
    const urlParams = new URLSearchParams(window.location.search);
    const payableId = urlParams.get('payable_id');
    if (payableId) {
        payableSelect.value = payableId;
        payableSelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}
