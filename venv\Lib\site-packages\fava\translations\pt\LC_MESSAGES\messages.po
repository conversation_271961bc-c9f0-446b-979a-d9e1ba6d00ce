msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: pt\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Erros"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Data"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Conta"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Beneficiário"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Declaração de Rendimentos"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Balanço"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Balancete"

#. Movimentos or Lançamentos...
#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Movimentos"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Consulta"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Património"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Mercados"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Eventos"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Editor"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Opções"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Estatísticas"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Ajuda"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Data"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "F"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Preço"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Custo"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Valor"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Saldo"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Guardar"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "A guardar..."

#. Wouldn't "arquivo" be better than "ficheiro"?
#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Ficheiro alterado. Clique para atualizar."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Expandir todas as contas"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Expandir tudo"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Movimentos de Conta"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Alterações"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Saldos"

#. Should variable names file and lineno be translated?
#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Mostrar fonte %(file)s:%(lineno)s"

#. Isn't "arquivo" better than "ficheiro"?
#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Arquivo"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Linha"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Erro"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Sem erros."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Evento:%(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Descrição"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Páginas de ajuda"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Moeda"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Moeda de custo"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Patrimônio por"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Lucro líquido"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Rendimento"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Despesas"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Chave"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Valor"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Guardar como"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Lançamentos por Conta"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Total"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Atualizações"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Clique para copiar os saldos de conta (exceto aqueles em verde) para a área de transferência."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Copiar saldos de conta"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Última Entrada"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Entradas por Tipo"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Tipo"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "# Entradas"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Descrição"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Número"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Importar"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Entradas do Orçamento"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Fonte"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Extrair"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "...digitar uma consulta BQL. 'ajuda' para listar comandos possíveis."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Metadado"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Adicionar metadado"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Nota"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Local"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Contexto"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr ""

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr ""

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr ""

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr ""

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr ""

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr ""

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr ""

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr ""

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr ""

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr ""

#: src/fava/templates/options.html:6
msgid "help"
msgstr ""

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr ""

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr ""

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr ""

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr ""

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr ""

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr ""

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr ""

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr ""

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr ""

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr ""

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr ""

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr ""

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr ""

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr ""

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr ""

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr ""

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr ""

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr ""

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr ""

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr ""

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr ""

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr ""

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr ""

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr ""

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr ""

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr ""

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr ""

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr ""

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr ""

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr ""

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr ""

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr ""

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr ""

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr ""

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

