{% extends "financial/base.html" %}

{% block title %}
{% if voucher %}编辑财务凭证{% else %}新建财务凭证{% endif %}
{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if voucher %}编辑财务凭证{% else %}新建财务凭证{% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 显示所有表单错误 -->
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> 表单验证失败</h6>
                        <ul class="mb-0">
                            {% for field_name, errors in form.errors.items() %}
                                {% for error in errors %}
                                <li><strong>{{ field_name }}:</strong> {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <form method="POST" id="voucherForm">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.voucher_date.label(class="form-label") }}
                                    {{ form.voucher_date(class="form-control") }}
                                    {% if form.voucher_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.voucher_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.voucher_type.label(class="form-label") }}
                                    {{ form.voucher_type(class="form-control") }}
                                    {% if form.voucher_type.errors %}
                                        <div class="text-danger">
                                            {% for error in form.voucher_type.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            {% if form.summary.errors %}
                                <div class="text-danger">
                                    {% for error in form.summary.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i>
                                {% if voucher %}更新凭证{% else %}创建凭证{% endif %}
                            </button>
                            <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                            <button type="button" class="btn btn-info" onclick="debugForm()">
                                <i class="fas fa-bug"></i> 调试表单
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 表单提交前的验证和调试
document.getElementById('voucherForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;

    // 显示提交状态
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
    submitBtn.disabled = true;

    // 验证表单数据
    const formData = new FormData(this);
    console.log('表单数据:', Object.fromEntries(formData));

    // 检查必填字段
    const voucherDate = document.querySelector('input[name="voucher_date"]').value;
    const voucherType = document.querySelector('select[name="voucher_type"]').value;
    const summary = document.querySelector('input[name="summary"]').value;

    console.log('字段值:', {
        voucher_date: voucherDate,
        voucher_type: voucherType,
        summary: summary
    });

    if (!voucherDate || !voucherType || !summary) {
        e.preventDefault();
        alert('请填写所有必填字段：凭证日期、凭证类型、摘要');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return false;
    }

    if (summary.length > 200) {
        e.preventDefault();
        alert('摘要长度不能超过200个字符');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return false;
    }
});

// 调试表单函数
function debugForm() {
    const formData = new FormData(document.getElementById('voucherForm'));
    const data = Object.fromEntries(formData);

    const debugInfo = {
        '表单数据': data,
        '当前URL': window.location.href,
        '用户代理': navigator.userAgent,
        '表单元素数量': document.getElementById('voucherForm').elements.length
    };

    console.log('调试信息:', debugInfo);

    // 显示调试信息
    let debugText = '=== 表单调试信息 ===\\n';
    for (const [key, value] of Object.entries(debugInfo)) {
        debugText += `${key}: ${JSON.stringify(value, null, 2)}\\n`;
    }

    alert(debugText);

    // 检查字段状态
    const fields = ['voucher_date', 'voucher_type', 'summary', 'notes'];
    fields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            console.log(`字段 ${fieldName}:`, {
                value: field.value,
                required: field.required,
                validity: field.validity
            });
        }
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认值
    const voucherDateField = document.querySelector('input[name="voucher_date"]');
    if (voucherDateField && !voucherDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        voucherDateField.value = today;
    }

    // 为摘要字段添加字符计数
    const summaryField = document.querySelector('input[name="summary"]');
    if (summaryField) {
        const charCounter = document.createElement('small');
        charCounter.className = 'text-muted';
        charCounter.id = 'summaryCounter';
        summaryField.parentNode.appendChild(charCounter);

        function updateCounter() {
            const length = summaryField.value.length;
            charCounter.textContent = `${length}/200 字符`;
            if (length > 200) {
                charCounter.className = 'text-danger';
            } else if (length > 180) {
                charCounter.className = 'text-warning';
            } else {
                charCounter.className = 'text-muted';
            }
        }

        summaryField.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
{% endblock %}
