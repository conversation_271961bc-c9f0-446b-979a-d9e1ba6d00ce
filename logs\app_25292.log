2025-06-08 19:11:19,723 INFO: 应用启动 - PID: 25292 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-08 19:12:02,353 INFO: 删除了 147 个现有系统科目 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:463]
2025-06-08 19:12:02,359 ERROR: 强制初始化系统会计科目失败: (pyodbc.IntegrityError) ('23000', "[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]不能将值 NULL 插入列 'area_id'，表 'StudentsCMSSP.dbo.accounting_subjects'；列不允许有 Null 值。INSERT 失败。 (515) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)")
[SQL: 
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (?, ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('1001', '库存现金', None, 1, '资产', '借方', None, True, True, '系统标准会计科目 - 库存现金', 34)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:551]
2025-06-08 19:12:07,320 INFO: 删除了 147 个有问题的科目 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:627]
2025-06-08 19:12:07,323 ERROR: 清理重复会计科目失败: (pyodbc.IntegrityError) ('23000', "[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]不能将值 NULL 插入列 'area_id'，表 'StudentsCMSSP.dbo.accounting_subjects'；列不允许有 Null 值。INSERT 失败。 (515) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)")
[SQL: 
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (?, ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('1001', '库存现金', None, 1, '资产', '借方', None, True, True, '系统标准会计科目 - 库存现金', 34)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:716]
2025-06-08 19:14:31,002 INFO: 删除了 147 个现有系统科目 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:463]
2025-06-08 19:14:31,007 ERROR: 强制初始化系统会计科目失败: (pyodbc.IntegrityError) ('23000', "[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]不能将值 NULL 插入列 'area_id'，表 'StudentsCMSSP.dbo.accounting_subjects'；列不允许有 Null 值。INSERT 失败。 (515) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)")
[SQL: 
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (?, ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('1001', '库存现金', None, 1, '资产', '借方', None, True, True, '系统标准会计科目 - 库存现金', 34)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:551]
2025-06-08 19:16:02,222 INFO: 删除了 147 个现有系统科目 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:463]
2025-06-08 19:16:02,230 ERROR: 强制初始化系统会计科目失败: (pyodbc.IntegrityError) ('23000', "[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]不能将值 NULL 插入列 'area_id'，表 'StudentsCMSSP.dbo.accounting_subjects'；列不允许有 Null 值。INSERT 失败。 (515) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)")
[SQL: 
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (?, ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('1001', '库存现金', None, 1, '资产', '借方', None, True, True, '系统标准会计科目 - 库存现金', 34)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:551]
