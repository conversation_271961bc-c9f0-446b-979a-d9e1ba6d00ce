2025-06-09 12:00:12,568 INFO: 应用启动 - PID: 23744 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:827]
2025-06-09 12:00:16,615 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-09 12:00:16,633 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-09 12:00:30,162 ERROR: 生成应付账款失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: INSERT INTO account_payables (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id, original_amount, paid_amount, balance_amount, due_date, status, payment_terms, invoice_number, invoice_date, invoice_amount, created_by, notes, created_at, updated_at) OUTPUT inserted.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('AP20250609001', 42, 1, 81, 48, Decimal('30000.00'), 0, Decimal('30000.00'), None, '未付款', None, None, None, None, 34, None, datetime.datetime(2025, 6, 9, 12, 0, 30), datetime.datetime(2025, 6, 9, 12, 0, 30))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\payables.py:275]
2025-06-09 12:00:46,402 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-09 12:00:46,427 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
