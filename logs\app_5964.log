2025-06-07 21:59:01,866 INFO: 应用启动 - PID: 5964 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-07 21:59:26,478 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 21:59:26,478 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 21:59:26,480 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 21:59:26,480 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 21:59:26,487 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:05,474 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:05,474 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:05,475 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:05,475 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:05,477 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:30,103 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:30,104 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:30,108 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:30,108 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:30,114 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:31,041 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:31,041 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:31,042 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:31,042 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:31,044 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:31,311 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:31,311 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:31,312 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:31,312 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:31,314 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:31,533 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:31,534 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:31,536 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:31,537 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:31,542 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:31,782 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:31,782 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:31,784 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:31,785 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:31,789 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
2025-06-07 22:01:32,041 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:01:32,041 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:01:32,042 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:01:32,042 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:01:32,044 ERROR: 获取消耗计划列表失败: 'ConsumptionPlan' object has no attribute 'menu_plan_id' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:263]
