{% set infotext = {
    'green':  "The last entry is a passing balance check.",
    'red':    "The last entry is a failing balance check.",
    'yellow': "The last entry is not a balance check.",
} %}

{% macro indicator(ledger, account) %}
{% set status=ledger.accounts[account].uptodate_status %}
{% set balance_string=ledger.accounts[account].balance_string or "" %}
{% if status %}
<copyable-text class="status-indicator status-{{ status }}" title="{{ infotext[status] }}

Click to copy the balance directives to the clipboard:

{{ balance_string }}" data-clipboard-text="{{ balance_string }}"></copyable-text>
{% endif %}
{% endmacro %}

{% macro last_account_activity(ledger, account_name) %}
{% set last_entry = ledger.accounts[account_name].last_entry%}
{% set last_account_activity = (today() - last_entry.date).days if last_entry else 0 %}
{% if last_account_activity > ledger.fava_options.uptodate_indicator_grey_lookback_days %}
    <span class="status-indicator status-gray" title="This account has not been updated in a while. ({{ last_account_activity }} days ago)"></span>
{% endif %}
{% endmacro %}

{% macro account_name(ledger, account_name) -%}
<a href="{{ url_for('account', name=account_name) }}" class="account">
  {{- account_name -}}
</a>
{%- if ledger.accounts[account_name].uptodate_status %}

{{ indicator(ledger, account_name) }}
{{ last_account_activity(ledger, account_name) }}
{% endif %}
{% endmacro %}
