{% import 'macros/_account_macros.html' as account_macros %}
{% import 'macros/_commodity_macros.html' as commodity_macros %}

{% set sort_type = {
    "<class 'decimal.Decimal'>": 'num',
    "<class 'cdecimal.Decimal'>": 'num',
    "<class 'int'>": 'num',
    "<class 'beancount.core.amount.Amount'>": 'num',
    "<class 'beancount.core.inventory.Inventory'>": 'num',
    "<class 'beancount.core.position.Position'>": 'num',
} %}

{% macro querycell(ledger, name, value, type_) %}
{% set type = type_|string %}
{% if type == "<class 'beancount.core.inventory.Inventory'>" %}
<td class="num">
  {% for position in value|sort(attribute='units.currency') %}
    {{ commodity_macros.render_amount(ledger, position.units) }}<br>
  {% endfor %}
</td>
{% elif type == "<class 'str'>" %}
<td>
  {% if name == "account" %}
    {{ account_macros.account_name(ledger, value) }}
  {% elif name == "id" %}
  <a href="#context-{{ value }}">{{ value }}</a>
  {% else %}
  {{ value }}
  {% endif %}
</td>
{% elif type == "<class 'decimal.Decimal'>" or type == "<class 'cdecimal.Decimal'>" %}
<td class="num" data-sort-value="{{ value or 0 }}">{{ value|format_currency }}</td>
{% elif type == "<class 'beancount.core.amount.Amount'>" %}
<td class="num" data-sort-value="{{ value.number or 0 }}">
  {{ commodity_macros.render_amount(ledger, value) }}
</td>
{% elif type == "<class 'bool'>" %}
<td>{{ value|upper }}</td>
{% elif type == "<class 'int'>" %}
<td class="num">{{ value }}</td>
{% elif type == "<class 'set'>" %}
<td>{{ value|join(',') }}</td>
{% elif type == "<class 'datetime.date'>" %}
<td>{{ value or '' }}</td>
{% elif type == "<class 'beancount.core.position.Position'>" %}
<td class="num">{{ commodity_macros.render_amount(ledger, value.units) }}</td>
{% else %}
<td class="query-error" title="Type {{ type|string }} not recognized">{{ value }}</td>
{% endif %}
{% endmacro %}

{% macro querytable(ledger, contents, types, rows, filter_empty=None, footer=None) %}
{% if contents %}
<pre><code>{{ contents }}</code></pre>
{% elif types %}
<table is="sortable-table" class="queryresults">
  <thead>
    <tr>
      {% for name, type in types %}
      <th data-sort="{{ sort_type[type|string] or "string" }}">{{ name }}</th>
      {% endfor %}
    </tr>
  </thead>
  <tbody>
    {% for row in rows if filter_empty == None or not row[filter_empty].is_empty() %}
    <tr>
      {% for name, type in types %}
      {{ querycell(ledger, name, row[name], type)  }}
      {% endfor %}
    </tr>
    {% endfor %}
  </tbody>
  {% if footer %}
  <tfoot>
    <tr>
      {% for type, value in footer %}
        {{ querycell(ledger, '', value, type)  }}
      {% endfor %}
    </tr>
  </tfoot>
  {% endif %}
</table>
{% endif %}
{% endmacro %}
