# xiaoyuanst.com 域名配置说明

## 服务器信息
- **域名**: xiaoyuanst.com
- **IP地址**: **************
- **Flask端口**: 8080
- **IIS端口**: 80

## 配置步骤

### 1. 自动配置（推荐）

以管理员身份运行PowerShell，执行以下命令：

```powershell
# 配置域名访问
.\setup_xiaoyuanst_domain.ps1

# 测试配置
.\test_xiaoyuanst_domain.ps1
```

### 2. 启动Flask应用

```powershell
# 启动Flask应用（端口8080）
python run.py
```

### 3. 验证配置

配置完成后，您可以通过以下方式访问应用：

- **本地访问**: http://localhost
- **IP访问**: http://**************
- **域名访问**: http://xiaoyuanst.com
- **带www访问**: http://www.xiaoyuanst.com

## 架构说明

```
外网用户 → xiaoyuanst.com:80 → IIS反向代理 → Flask应用:8080
```

- **IIS**: 接收外网请求（端口80），处理静态文件
- **URL Rewrite**: 将动态请求转发给Flask应用（端口8080）
- **Flask**: 处理业务逻辑，返回动态内容

## 配置文件说明

### web.config
IIS反向代理配置文件，将请求转发到Flask应用：

```xml
<action type="Rewrite" url="http://127.0.0.1:8080/{R:1}" />
```

### run.py
Flask应用启动配置，绑定到端口8080：

```python
app.run(debug=1, host='127.0.0.1', port=8080, use_reloader=False, threaded=True)
```

## 故障排除

### 1. 检查服务状态

```powershell
# 检查IIS站点状态
Get-Website -Name "xiaoyuanst.com"

# 检查应用程序池状态
Get-WebAppPool -Name "xiaoyuanst.com_AppPool"

# 检查端口占用
netstat -ano | findstr ":80"
netstat -ano | findstr ":8080"
```

### 2. 检查防火墙规则

```powershell
# 查看防火墙规则
Get-NetFirewallRule -DisplayName "*xiaoyuanst*"
```

### 3. 重启服务

```powershell
# 重启IIS
iisreset

# 重启应用程序池
Restart-WebAppPool -Name "xiaoyuanst.com_AppPool"

# 重启站点
Stop-Website -Name "xiaoyuanst.com"
Start-Website -Name "xiaoyuanst.com"
```

### 4. 常见问题

#### 问题1: 域名无法访问
- 检查DNS记录是否指向正确的IP地址（**************）
- 检查防火墙是否允许端口80访问
- 确认IIS站点正在运行

#### 问题2: 502错误
- 检查Flask应用是否在端口8080上运行
- 检查web.config配置是否正确
- 查看IIS日志文件

#### 问题3: 403错误
- 检查站点权限设置
- 确认IIS_IUSRS和IUSR用户有适当权限

## DNS配置

确保域名DNS记录正确配置：

```
类型: A记录
主机: @
值: **************

类型: A记录
主机: www
值: **************
```

## 安全建议

1. **防火墙配置**: 只开放必要的端口（80, 443）
2. **SSL证书**: 建议配置HTTPS（端口443）
3. **访问日志**: 定期检查IIS访问日志
4. **更新维护**: 定期更新IIS和Windows系统

## 监控和维护

### 日志文件位置
- **IIS日志**: `C:\inetpub\logs\LogFiles\`
- **Flask日志**: 应用程序目录下的logs文件夹

### 性能监控
```powershell
# 检查CPU和内存使用情况
Get-Process -Name "w3wp" | Select-Object CPU, WorkingSet
Get-Process -Name "python" | Select-Object CPU, WorkingSet
```

## 联系信息

如有问题，请检查：
1. 服务器配置指南.md
2. 运行测试脚本：`.\test_xiaoyuanst_domain.ps1`
3. 查看IIS和Flask应用日志
