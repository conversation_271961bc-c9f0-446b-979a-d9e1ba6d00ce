2025-06-08 12:25:51,193 INFO: 应用启动 - PID: 16060 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-08 12:26:06,932 INFO: 表单提交数据: ImmutableMultiDict([('voucher_date', '2025-06-08'), ('voucher_type', '入库凭证'), ('summary', '33333333333'), ('notes', '梦工')]) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:91]
2025-06-08 12:26:06,933 INFO: 表单验证结果: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:92]
2025-06-08 12:26:06,933 INFO: 表单错误: {} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:93]
2025-06-08 12:26:06,934 ERROR: 创建财务凭证失败: local variable 'FinancialVoucher' referenced before assignment [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:141]
2025-06-08 12:26:06,934 ERROR: 详细错误信息: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py", line 102, in create_voucher
    last_voucher = FinancialVoucher.query.filter(
UnboundLocalError: local variable 'FinancialVoucher' referenced before assignment
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:142]
