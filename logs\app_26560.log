2025-06-07 21:29:40,573 INFO: 应用启动 - PID: 26560 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-07 21:32:36,850 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 21:32:36,867 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 21:32:51,448 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                INSERT INTO financial_vouchers 
                (voucher_number, voucher_date, area_id, voucher_type, summary, 
                 total_amount, status, source_type, created_by, notes)
                OUTPUT inserted.id
                VALUES 
                (?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('PZ20250607001', datetime.date(2025, 6, 7), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', 34, '333333')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:140]
2025-06-07 21:33:19,520 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 21:33:19,532 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 21:33:28,702 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 21:35:15,122 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 21:35:15,129 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
