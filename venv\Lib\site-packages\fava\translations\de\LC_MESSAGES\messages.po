msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: de\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Fehler"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Zeitraum"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Konto"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Empfänger"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Gewinn- und Verlustrechnung"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Bilanz"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Zwischenbilanz"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Journal"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Abfrage"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Bestände"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Währungen"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Ereignisse"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Editor"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Optionen"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Statistiken"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Hilfe"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Datum"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "M"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Preis"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Kosten"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Änderung"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Saldo"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Speichern"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Wird gespeichert …"

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Änderung in Quelldatei entdeckt. Klicken, um neu zu laden."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Alle Konten erweitern"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Alle erweitern"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Konto-Journal"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Änderungen"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Salden"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Quelldatei anzeigen: %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Datei"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Zeile"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Fehler"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Keine Fehler."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Ereignis: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Beschreibung"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Hilfeseiten"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Währung"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Kostenwährung"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Bestände nach"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Reingewinn"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Einkommen"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Ausgaben"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Schlüssel"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Wert"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Herunterladen als"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Einträge pro Konto"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Gesamt"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Aktualisierungs-Aktivität"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Klicken, um Saldo-Einträge für Konten (außer den grünen) in die Zwischenablage zu kopieren."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Saldo-Einträge kopieren"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Letzter Eintrag"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Einträge pro Art"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Art"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "Anzahl Einträge"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Beschreibung"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Zahl"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Importieren"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Budget-Einträge"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Quellcode"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Extrahieren"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "… eine BQL-Abfrage eingeben. »help«, um verfügbare Befehle aufzulisten."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Metadaten"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Metadaten hinzufügen"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Notiz"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Speicherort"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Kontext"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Salden vor dem Eintrag"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Salden nach dem Eintrag"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Erledigte Transaktionen"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Unerledigte Transaktionen"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Andere Transaktionen"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Dokumente mit der Markierung #discovered"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Einträge"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Datei(en) hochladen"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Hochladen"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Beträge bündig ausrichten"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Kommentar umschalten (für die Auswahl)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Alle eingeklappten Bereiche öffnen"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Alle ausgeklappten Bereiche schließen"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Optionen für Fava"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "Hilfe"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Optionen für Beancount"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Abschicken"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Halten Sie beim Klicken die Umschalt-Taste gedrückt, um alle Unterkonten aufzuklappen.\n"
"Halten Sie beim Klicken die Strg- oder Cmd-Taste gedrückt, um eine Ebene aufzuklappen."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Exportieren"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Filtern nach Markierung, Empfänger, …"

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Die aktuell gefilterten Einträge als Beancount-Datei herunterladen"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Jährlich"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Quartalsweise"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Monatlich"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Wöchentlich"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Täglich"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Bearbeiten"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Betrag"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Dokumente mit der Markierung #linked"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "Einkaufspreis"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "Marktwert"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "Einheiten"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "Kacheldiagramm"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "Sunburst"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "Muss eines der deklarierten Konten sein"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "Zurück"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "Weiter"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "Transaktion"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "Hinzufügen"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "Dokument verschieben oder umbenennen"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "Name"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "Dokumente"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "Entfernen"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "Liniendiagramm"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "Flächendiagramm"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "Fortsetzen"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "Leeren"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "Zum Konto"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr "Diese Datei entfernen?"

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr "Keine Dateien zum Importieren gefunden."

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr "%(type)s Einträge umschalten"

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr "Umgerechnet in %(currency)s"

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr "Gestapeltes Säulendiagramm"

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr "Einfaches Säulendiagramm"

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr "Leeres Diagramm"

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr "Metadaten umschalten"

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr "Einträge umschalten"

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr "Reinvermögen"

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr "Saldo"

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr "neu laden"

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr "Wird gelöscht..."

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr "Journal-Eintrag hinzufügen"

