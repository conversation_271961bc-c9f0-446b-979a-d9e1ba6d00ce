{% extends "financial/base.html" %}

{% block title %}付款记录管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">付款记录管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.create_payment') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 新建付款记录
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" 
                                           value="{{ keyword }}" placeholder="付款号或摘要">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="supplier_id">供应商</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id">
                                        <option value="">-- 所有供应商 --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="payment_method">付款方式</label>
                                    <select class="form-control" id="payment_method" name="payment_method">
                                        <option value="">-- 所有方式 --</option>
                                        <option value="现金" {% if payment_method == '现金' %}selected{% endif %}>现金</option>
                                        <option value="银行转账" {% if payment_method == '银行转账' %}selected{% endif %}>银行转账</option>
                                        <option value="支票" {% if payment_method == '支票' %}selected{% endif %}>支票</option>
                                        <option value="其他" {% if payment_method == '其他' %}selected{% endif %}>其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">开始日期</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">结束日期</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        <a href="{{ url_for('financial.payments_index') }}" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-undo"></i> 重置
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 付款记录列表 -->
                    {% if payments.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>付款编号</th>
                                    <th>付款日期</th>
                                    <th>供应商</th>
                                    <th>应付账款号</th>
                                    <th>付款金额</th>
                                    <th>付款方式</th>
                                    <th>摘要</th>
                                    <th>创建人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments.items %}
                                <tr>
                                    <td>{{ payment.payment_number }}</td>
                                    <td>{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '未知' }}</td>
                                    <td>{{ payment.supplier.name if payment.supplier else '未知供应商' }}</td>
                                    <td>{{ payment.payable.payable_number if payment.payable else '-' }}</td>
                                    <td class="text-right">{{ "%.2f"|format(payment.amount) }}</td>
                                    <td>{{ payment.payment_method }}</td>
                                    <td>{{ payment.summary }}</td>
                                    <td>{{ payment.creator.username if payment.creator else '未知' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-info btn-sm" title="查看详情" 
                                                    onclick="viewPayment({{ payment.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if payment.voucher_id %}
                                            <a href="{{ url_for('financial.view_voucher', id=payment.voucher_id) }}" 
                                               class="btn btn-success btn-sm" title="查看凭证">
                                                <i class="fas fa-file-invoice"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="font-weight-bold">
                                    <td colspan="4" class="text-right">合计金额：</td>
                                    <td class="text-right">{{ "%.2f"|format(payments.items|sum(attribute='amount')) }}</td>
                                    <td colspan="4"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if payments.pages > 1 %}
                    <nav aria-label="付款记录分页">
                        <ul class="pagination justify-content-center">
                            {% if payments.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.payments_index', page=payments.prev_num, keyword=keyword, supplier_id=supplier_id, payment_method=payment_method, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in payments.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != payments.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.payments_index', page=page_num, keyword=keyword, supplier_id=supplier_id, payment_method=payment_method, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if payments.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.payments_index', page=payments.next_num, keyword=keyword, supplier_id=supplier_id, payment_method=payment_method, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无付款记录数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewPayment(paymentId) {
    alert('查看付款记录详情功能待实现，ID: ' + paymentId);
}
</script>
{% endblock %}
