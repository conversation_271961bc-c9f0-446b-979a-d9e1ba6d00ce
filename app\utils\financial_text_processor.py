"""
财务文本处理工具
基于现有库实现财务数据的文本格式化和处理
"""

import pandas as pd
from datetime import datetime
from decimal import Decimal
import textwrap
from typing import List, Dict, Any, Optional
from beancount.core import data
from beancount.core.number import D
from beancount.core.amount import Amount
from beancount.core.account import Account
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
import io


class FinancialTextProcessor:
    """财务文本处理器"""
    
    def __init__(self):
        self.text_width = 80  # 默认文本宽度
        self.currency_symbol = "¥"
        
    def format_voucher_text(self, voucher_data: Dict[str, Any]) -> str:
        """
        将财务凭证转换为固定宽度的文本格式
        """
        lines = []
        
        # 凭证头部信息
        header = self._create_voucher_header(voucher_data)
        lines.extend(header)
        
        # 分隔线
        lines.append("=" * self.text_width)
        
        # 凭证明细表格
        details_table = self._create_details_table(voucher_data.get('details', []))
        lines.extend(details_table)
        
        # 分隔线
        lines.append("=" * self.text_width)
        
        # 合计行
        totals = self._create_totals_section(voucher_data.get('details', []))
        lines.extend(totals)
        
        # 签字区域
        signatures = self._create_signature_section(voucher_data)
        lines.extend(signatures)
        
        return "\n".join(lines)
    
    def _create_voucher_header(self, voucher_data: Dict[str, Any]) -> List[str]:
        """创建凭证头部信息"""
        lines = []
        
        # 标题居中
        title = f"记账凭证 - {voucher_data.get('voucher_number', '')}"
        lines.append(title.center(self.text_width))
        lines.append("")
        
        # 基本信息
        voucher_type = voucher_data.get('voucher_type', '')
        voucher_date = voucher_data.get('voucher_date', '')
        attachment_count = voucher_data.get('attachment_count', 0)
        
        info_line = f"类型: {voucher_type:<12} 日期: {voucher_date:<12} 附件: {attachment_count}张"
        lines.append(info_line)
        lines.append("")
        
        return lines
    
    def _create_details_table(self, details: List[Dict[str, Any]]) -> List[str]:
        """创建凭证明细表格"""
        lines = []
        
        # 表头
        header = f"{'行号':<4} {'摘要':<20} {'会计科目':<25} {'借方金额':<12} {'贷方金额':<12}"
        lines.append(header)
        lines.append("-" * self.text_width)
        
        # 明细行
        for i, detail in enumerate(details, 1):
            summary = detail.get('summary', '')[:18] + '..' if len(detail.get('summary', '')) > 20 else detail.get('summary', '')
            
            subject_name = detail.get('subject', {}).get('name', '')
            subject_code = detail.get('subject', {}).get('code', '')
            subject_display = f"{subject_code}-{subject_name}"[:23] + '..' if len(f"{subject_code}-{subject_name}") > 25 else f"{subject_code}-{subject_name}"
            
            debit_amount = self._format_amount(detail.get('debit_amount', 0))
            credit_amount = self._format_amount(detail.get('credit_amount', 0))
            
            line = f"{i:<4} {summary:<20} {subject_display:<25} {debit_amount:<12} {credit_amount:<12}"
            lines.append(line)
        
        return lines
    
    def _create_totals_section(self, details: List[Dict[str, Any]]) -> List[str]:
        """创建合计部分"""
        lines = []
        
        total_debit = sum(float(detail.get('debit_amount', 0)) for detail in details)
        total_credit = sum(float(detail.get('credit_amount', 0)) for detail in details)
        
        debit_str = self._format_amount(total_debit)
        credit_str = self._format_amount(total_credit)
        
        total_line = f"{'合计':<49} {debit_str:<12} {credit_str:<12}"
        lines.append(total_line)
        
        # 平衡检查
        balance_status = "借贷平衡" if abs(total_debit - total_credit) < 0.01 else f"不平衡(差额: {abs(total_debit - total_credit):.2f})"
        lines.append(f"平衡状态: {balance_status}")
        lines.append("")
        
        return lines
    
    def _create_signature_section(self, voucher_data: Dict[str, Any]) -> List[str]:
        """创建签字区域"""
        lines = []
        
        created_by = voucher_data.get('created_by', '')
        reviewed_by = voucher_data.get('reviewed_by', '')
        posted_by = voucher_data.get('posted_by', '')
        
        signature_line = f"制单: {created_by:<10} 审核: {reviewed_by:<10} 记账: {posted_by:<10} 出纳: {'':10}"
        lines.append(signature_line)
        
        return lines
    
    def _format_amount(self, amount: float) -> str:
        """格式化金额显示"""
        if amount == 0:
            return ""
        return f"{self.currency_symbol}{amount:,.2f}"
    
    def format_subject_tree_text(self, subjects: List[Dict[str, Any]]) -> str:
        """
        实现科目层级的缩进文本表示
        """
        lines = []
        lines.append("会计科目树状结构")
        lines.append("=" * 60)
        
        # 按科目类型分组
        subject_groups = self._group_subjects_by_type(subjects)
        
        for subject_type, type_subjects in subject_groups.items():
            lines.append(f"\n{self._get_subject_type_symbol(subject_type)} {subject_type}")
            lines.append("-" * 40)
            
            for subject in type_subjects:
                level = subject.get('level', 0)
                indent = "  " * level
                code = subject.get('code', '')
                name = subject.get('name', '')
                balance = subject.get('balance', 0)
                
                balance_str = self._format_amount(balance) if balance != 0 else ""
                line = f"{indent}├─ {code} {name:<20} {balance_str:>12}"
                lines.append(line)
        
        return "\n".join(lines)
    
    def _group_subjects_by_type(self, subjects: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按科目类型分组"""
        groups = {}
        for subject in subjects:
            subject_type = subject.get('subject_type', '其他')
            if subject_type not in groups:
                groups[subject_type] = []
            groups[subject_type].append(subject)
        return groups
    
    def _get_subject_type_symbol(self, subject_type: str) -> str:
        """获取科目类型符号"""
        symbols = {
            '资产': '💰',
            '负债': '📋',
            '所有者权益': '🏛️',
            '收入': '📈',
            '费用': '📉',
            '成本': '🔧'
        }
        return symbols.get(subject_type, '📁')
    
    def generate_balance_sheet_text(self, balance_data: Dict[str, Any]) -> str:
        """
        生成资产负债表的文本格式
        """
        lines = []
        
        # 标题
        title = "资产负债表"
        date_str = balance_data.get('date', datetime.now().strftime('%Y-%m-%d'))
        lines.append(title.center(80))
        lines.append(f"报表日期: {date_str}".center(80))
        lines.append("=" * 80)
        
        # 资产部分
        lines.append("\n资产")
        lines.append("-" * 40)
        assets = balance_data.get('assets', [])
        for asset in assets:
            name = asset.get('name', '')
            amount = self._format_amount(asset.get('amount', 0))
            lines.append(f"{name:<30} {amount:>15}")
        
        # 负债部分
        lines.append("\n负债")
        lines.append("-" * 40)
        liabilities = balance_data.get('liabilities', [])
        for liability in liabilities:
            name = liability.get('name', '')
            amount = self._format_amount(liability.get('amount', 0))
            lines.append(f"{name:<30} {amount:>15}")
        
        # 所有者权益部分
        lines.append("\n所有者权益")
        lines.append("-" * 40)
        equity = balance_data.get('equity', [])
        for eq in equity:
            name = eq.get('name', '')
            amount = self._format_amount(eq.get('amount', 0))
            lines.append(f"{name:<30} {amount:>15}")
        
        return "\n".join(lines)
    
    def generate_income_statement_text(self, income_data: Dict[str, Any]) -> str:
        """
        创建利润表的文本表示
        """
        lines = []
        
        # 标题
        title = "利润表"
        period = income_data.get('period', '')
        lines.append(title.center(80))
        lines.append(f"报告期间: {period}".center(80))
        lines.append("=" * 80)
        
        # 收入部分
        lines.append("\n一、营业收入")
        revenues = income_data.get('revenues', [])
        for revenue in revenues:
            name = revenue.get('name', '')
            amount = self._format_amount(revenue.get('amount', 0))
            lines.append(f"  {name:<28} {amount:>15}")
        
        # 成本费用部分
        lines.append("\n二、营业成本和费用")
        costs = income_data.get('costs', [])
        for cost in costs:
            name = cost.get('name', '')
            amount = self._format_amount(cost.get('amount', 0))
            lines.append(f"  {name:<28} {amount:>15}")
        
        # 利润计算
        total_revenue = sum(r.get('amount', 0) for r in revenues)
        total_costs = sum(c.get('amount', 0) for c in costs)
        net_profit = total_revenue - total_costs
        
        lines.append("\n" + "-" * 50)
        lines.append(f"三、净利润 {self._format_amount(net_profit):>30}")
        
        return "\n".join(lines)
    
    def export_to_text_file(self, content: str, filename: str) -> str:
        """
        导出文本内容到文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return f"文本文件已导出: {filename}"
        except Exception as e:
            return f"导出失败: {str(e)}"
    
    def create_pandas_report(self, data: List[Dict[str, Any]], title: str = "财务报表") -> str:
        """
        使用 Pandas 生成格式化文本报表
        """
        if not data:
            return "无数据可显示"
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 格式化数值列
        for col in df.columns:
            if df[col].dtype in ['float64', 'int64'] and 'amount' in col.lower():
                df[col] = df[col].apply(lambda x: f"{self.currency_symbol}{x:,.2f}" if pd.notna(x) and x != 0 else "")
        
        # 生成文本表格
        text_table = df.to_string(index=False, justify='left')
        
        # 添加标题和边框
        lines = []
        lines.append(title.center(len(text_table.split('\n')[0])))
        lines.append("=" * len(text_table.split('\n')[0]))
        lines.append(text_table)
        
        return "\n".join(lines)


class BeanCountIntegration:
    """Beancount 集成工具"""
    
    def __init__(self):
        self.processor = FinancialTextProcessor()
    
    def convert_voucher_to_beancount(self, voucher_data: Dict[str, Any]) -> str:
        """
        将凭证转换为 Beancount 格式
        """
        lines = []
        
        # 日期和描述
        date_str = voucher_data.get('voucher_date', datetime.now().strftime('%Y-%m-%d'))
        description = voucher_data.get('summary', f"凭证 {voucher_data.get('voucher_number', '')}")
        
        lines.append(f"{date_str} * \"{description}\"")
        
        # 明细行
        details = voucher_data.get('details', [])
        for detail in details:
            account = self._convert_to_beancount_account(detail.get('subject', {}))
            debit_amount = detail.get('debit_amount', 0)
            credit_amount = detail.get('credit_amount', 0)
            
            if debit_amount > 0:
                lines.append(f"  {account:<40} {debit_amount:.2f} CNY")
            elif credit_amount > 0:
                lines.append(f"  {account:<40} -{credit_amount:.2f} CNY")
        
        return "\n".join(lines)
    
    def _convert_to_beancount_account(self, subject: Dict[str, Any]) -> str:
        """
        转换会计科目为 Beancount 账户格式
        """
        subject_type = subject.get('subject_type', '')
        code = subject.get('code', '')
        name = subject.get('name', '')
        
        # 映射到 Beancount 账户类型
        type_mapping = {
            '资产': 'Assets',
            '负债': 'Liabilities',
            '所有者权益': 'Equity',
            '收入': 'Income',
            '费用': 'Expenses',
            '成本': 'Expenses'
        }
        
        beancount_type = type_mapping.get(subject_type, 'Assets')
        account_name = f"{code}-{name}".replace(' ', '-').replace('(', '').replace(')', '')
        
        return f"{beancount_type}:{account_name}"
