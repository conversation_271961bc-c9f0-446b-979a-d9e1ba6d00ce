msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: pt-br\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Erros"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Data"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Conta"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Beneficiário"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Apuração do Resultado"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Balanço Patrimonial"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Balanço de Verificação"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Extrato"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Consulta"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Posições"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Commodities"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Eventos"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Editor"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Opções"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Estatísticas"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Ajuda"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Data"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "S"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Preço"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Custo"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Valor"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Saldo"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Salvar"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Salvando..."

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Arquivo modificado. Clique para atualizar."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Expandir todas as contas"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Expandir tudo"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Extrato da Conta"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Valores"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Saldos"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Mostrar fonte %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Arquivo"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Linha"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Erro"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Sem erros."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Evento: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Descrição"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Páginas de ajuda"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Moeda"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Moeda do Custo"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Posições por"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Lucro Líquido"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Receita"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Despesas"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Chave"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Valor"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Baixar como"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Lançamentos por Conta"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Total"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Atividades de Atualização"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Clique para copiar declarações de saldo para contas (exceto verdes) para a área de transferência."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Copiar declarações de saldo"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Última Entrada"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Entradas por Tipo"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Tipo"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "# Entradas"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Descrição"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Número"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Importar"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Entradas do Orçamento"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Fonte"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Extrair"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "...digitar uma consulta BQL. 'ajuda' para listar comandos possíveis."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Metadado"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Adicionar metadado"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Nota"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Local"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Contexto"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Saldos antes da entrada"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Saldos depois da entrada"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Transações confirmadas"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Transações pendentes"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Outras transações"

#. The tag cannot be translated?
#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Documentos com uma tag #discovered"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Lançamentos"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Enviar arquivo(s)"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Enviar"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Alinhar Valores"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Alternar Comentário (seleção)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Abrir todos os nós"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Fechar todos os nós"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Opções do Fava"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "ajuda"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Opções do Beancount"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Enviar"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Segure Shift enquanto clica para expandir todos os subnós. \n"
"Segure Ctrl ou Cmd enquanto clica para expandir apenas o primeiro nível."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Exportar"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Filtrar por tag, beneficiário, ..."

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Baixar entradas filtradas como um arquivo Beancount"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Anual"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Trimestral"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Mensal"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Semanal"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Diário"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Editar"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Valor"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Documentos com uma tag #linked"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "Em Custo"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "Em Valor de Mercado"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "Unidades"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "Treemap"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "Sunburst"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "Deve ser uma das contas declaradas"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "Anterior"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "Próximo"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "Transação"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "Adicionar"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "Mover ou renomear documento"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "Nome"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "Documentos"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "Apagar"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "Gráfico de Linhas"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "Gráfico de Área"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "Continuar"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "Recomeçar"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "Ir para conta"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr "Apagar este arquivo?"

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr "Nenhum arquivo encontrado para importar."

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr "Alternar entradas  %(type)s"

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr "Convertido para %(currency)s"

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr "Barras empilhadas"

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr "Barras simples"

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr "Gráfico está vazio."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr "Alternar exibição metadado"

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr "Alternar exibição lançamentos"

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr "Patrimônio Líquido"

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr "Saldo da Conta"

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr "recarregar"

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr "continuar"

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr "Apagando..."

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr "Adicionar Entrada"

