msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: sv\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Fel"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Tid"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Konto"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Mottagare"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Resultaträkning"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Balansräkning"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Råbalans"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Huvudbok"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Uppslag"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Innehav"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Handelsvaror"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Händelser"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Redigerare"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Inställningar"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Statistik"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Hjälp"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Datum"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "M"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Pris"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Kostnad"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Ändra"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Saldo"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Spara"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Sparar…"

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Filändring upptäckt. Klicka för att ladda om."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Expandera alla konton"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Expandera alla"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Kontojournal"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Ändringar"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Saldon"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Visa källa: %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Fil"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Linje"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Fel"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Inga fel."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Händelse: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Beskrivning"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Hjälpsidor"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Valuta"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Valutakostnad"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Innehav av"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Nettoresultat"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Intäkter"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Utgifter"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Nyckel"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Värde"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Ladda ner som"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Poster per konto"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Total"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Uppdatera aktivitet"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Klicka för att kopiera balansdirektkiven för konotn (förutom de gröna) till urklipp."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Kopiera balansdirektkiv"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Senaste inlägg"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Inlägg per typ"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Typ"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "Antal poster"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Beskrivning"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Antal"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Importera"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Budget-inlägg"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Källkod"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Extrahera"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "… eine BQL-Abfrage eingeben. »help«, um verfügbare Befehle aufzulisten."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Metadata"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Lägg till metadata"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Anteckning"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Plats"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Kontext"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Saldon innan inlägg"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Saldon efter inlägg"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Färdiga transaktioner"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Väntande transaktioner"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Andra transaktioner"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Dokument med etiketten #discovered"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Poster"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Ladda upp fil(er)"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Ladda upp"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Justera belopp"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Toggla kommentar (för markering)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Öppna alla sektioner"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Stäng alla sektioner"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Fava-inställningar"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "hjälp"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Beancount-inställningar"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Skicka"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Håll inne Shift medan du klickar för att expandera alla undernivåer. \n"
" Håll inne Ctrl eller Cmd medan du klickar för att expandera en nivå."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Exportera"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Filtrera efter etikett, mottagare, …"

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Ladda ner filtrerade inlägg som en Beancount-fil"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Årligt"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Kvartalsvis"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Månatligt"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Veckovis"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Dagligt"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Redigera"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Belopp"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Dokument med etiketten #linked"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "Kostnad"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "Marknadsvärde"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "Enheter"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "Trädkarta"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "Sunburst"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "Bör vara ett av de deklarerade kontona"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "Föregående"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "Nästa"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "Transaktion"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "Lägg till:"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "Flytta eller byta dokument"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "Namn"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "Dokument"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "Radera"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "Linjediagram"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "Områdesdiagram"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "Fortsätt"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "Rensa"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "Gå till konto"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr "Radera denna fil?"

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr "Inga filer för import kunde hittas."

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr ""

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr ""

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr ""

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr ""

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr ""

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr ""

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr ""

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

