2025-06-07 21:39:11,065 INFO: 应用启动 - PID: 3048 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-07 21:39:15,933 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                INSERT INTO financial_vouchers 
                (voucher_number, voucher_date, area_id, voucher_type, summary, 
                 total_amount, status, source_type, created_by, notes)
                OUTPUT inserted.id
                VALUES 
                (?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?)
            ]
[parameters: ('PZ20250607001', datetime.date(2025, 6, 7), 42, '入库凭证', '33333333333', 0, '草稿', '手工录入', 34, '333333')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:140]
