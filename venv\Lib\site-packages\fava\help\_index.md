Welcome to the help pages for Fava! You are running Beancount version
`{{ beancount_version }}` and Fava `{{ fava_version }}`
([changelog](https://beancount.github.io/fava/changelog.html)). There are help
pages for the following topics:

- [Beancount Syntax](./beancount_syntax) - short overview of the syntax.
- [Budgets](./budgets) - how to use Fava's budgeting feature.
- [Fava's Features](./features) - the features in detail.
- [Filtering entries](./filters) - how to filter the entries.
- [Extensions](./extensions) - how Fava can be extended.
- [Conversion](./conversion) - how to convert between currencies.
- [Import](./import) - the import system.
- [Options](./options) - the available options.

Fava comes with keyboard shortcuts - press <kbd>?</kbd> on any page to see the
available ones.

If you started Fava from the command line, you can run `fava --help` to see all
the available command line options.

If you discover a bug in Fava, or have some ideas for improvement, please open a
[bug report](https://github.com/beancount/fava/issues).

### Related websites

- Fava's [website](https://beancount.github.io/fava/),
  [chat](https://gitter.im/beancount/fava) and Fava on
  [GitHub](https://github.com/beancount/fava),
- Beancount's [documentation](http://furius.ca/beancount/doc/index),
  [mailing list](https://groups.google.com/forum/#!forum/beancount), and
  [bug tracker](https://bitbucket.org/blais/beancount/issues),
- An overview of other implementations of command-line accounting:
  [Plain Text Accounting](http://plaintextaccounting.org).
