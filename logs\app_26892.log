2025-06-07 22:19:43,153 INFO: 应用启动 - PID: 26892 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-07 22:19:53,404 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 22:19:53,423 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 22:19:58,095 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-07 22:20:00,899 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-07 22:20:11,395 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-07 22:20:11,623 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-07 22:20:12,000 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 22:20:12,015 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 22:20:27,381 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 22:20:27,398 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 22:20:34,036 INFO: 使用消耗计划的区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-07 22:20:34,037 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-03, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-07 22:20:34,037 INFO: 查询周菜单：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-07 22:20:34,041 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-07 22:20:34,041 INFO:   - 周菜单ID: 37, 开始日期: 2025-06-02, 结束日期: 2025-06-08, 状态: 已发布 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-07 22:20:34,047 INFO: 周菜单 37 总共有 77 条食谱记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-07 22:20:34,047 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,047 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,047 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,048 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,049 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,050 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,051 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,052 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,053 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,054 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,055 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,055 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,055 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,055 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:20:34,064 INFO: 匹配条件 day_of_week=2, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-07 22:20:34,064 INFO: 从周菜单读取到 0 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-07 22:20:34,064 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-07 22:20:34,068 INFO: 步骤1: 读取消耗日期: 2025-06-03, 餐次: 早餐+午餐+晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-07 22:20:34,072 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-07 22:20:34,076 INFO: 步骤2: 为出库食材 '鸡肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,079 INFO: 步骤2: 为出库食材 '羊肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,081 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,083 INFO: 步骤2: 为出库食材 '莲藕' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,084 INFO: 步骤2: 为出库食材 '韭菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,085 INFO: 步骤2: 为出库食材 '木耳' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,086 INFO: 步骤2: 为出库食材 '蒜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,086 INFO: 步骤2: 为出库食材 '吐司' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,087 INFO: 步骤2: 为出库食材 '芹菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,088 INFO: 步骤2: 为出库食材 '南瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,088 INFO: 步骤2: 为出库食材 '黄瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,089 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,089 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:20:34,089 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-07 22:20:44,897 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 22:20:44,905 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 22:20:48,345 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:20:48,345 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:20:48,347 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:20:48,347 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:20:55,606 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-07', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-07 22:20:55,608 INFO: 查询日期: 2025-06-07, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-07 22:20:55,608 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-07 22:20:55,608 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-07 22:20:58,404 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-07', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-07 22:20:58,405 INFO: 查询日期: 2025-06-07, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-07 22:20:58,405 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-07 22:20:58,406 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-07 22:20:58,424 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-07 22:20:58,425 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-07 22:21:00,652 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-07', 'meal_types': ['早餐', '午餐', '晚餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-07 22:21:00,653 INFO: 查询日期: 2025-06-07, 区域: 42, 餐次: ['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-07 22:21:00,653 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-07 22:21:00,655 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-07 22:21:00,671 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-07 22:21:00,671 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-07 22:21:00,681 INFO: 查询餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-07 22:21:00,681 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-07 22:21:08,004 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 22:21:08,004 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 22:21:08,005 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 22:21:08,005 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 22:21:08,412 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 22:21:08,424 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 22:25:21,925 INFO: 使用消耗计划的区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-07 22:25:21,926 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-03, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-07 22:25:21,927 INFO: 查询周菜单：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-07 22:25:21,931 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-07 22:25:21,931 INFO:   - 周菜单ID: 37, 开始日期: 2025-06-02, 结束日期: 2025-06-08, 状态: 已发布 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-07 22:25:21,935 INFO: 周菜单 37 总共有 77 条食谱记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-07 22:25:21,935 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,935 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,935 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,935 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,936 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,937 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=2, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,938 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=3, meal_type='晚餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,939 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='午餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=4, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,940 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,941 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-07 22:25:21,943 INFO: 匹配条件 day_of_week=2, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-07 22:25:21,943 INFO: 从周菜单读取到 0 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-07 22:25:21,944 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-07 22:25:21,948 INFO: 步骤1: 读取消耗日期: 2025-06-03, 餐次: 早餐+午餐+晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-07 22:25:21,950 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-07 22:25:21,951 INFO: 步骤2: 为出库食材 '鸡肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,952 INFO: 步骤2: 为出库食材 '羊肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,953 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,954 INFO: 步骤2: 为出库食材 '莲藕' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,954 INFO: 步骤2: 为出库食材 '韭菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,955 INFO: 步骤2: 为出库食材 '木耳' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,955 INFO: 步骤2: 为出库食材 '蒜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,956 INFO: 步骤2: 为出库食材 '吐司' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,957 INFO: 步骤2: 为出库食材 '芹菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,957 INFO: 步骤2: 为出库食材 '南瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,958 INFO: 步骤2: 为出库食材 '黄瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,958 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,959 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 22:25:21,959 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-07 22:26:19,435 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 22:26:19,451 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
