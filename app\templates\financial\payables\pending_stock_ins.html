{% extends "financial/base.html" %}

{% block title %}待生成应付账款的入库单{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">待生成应付账款的入库单</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.payables_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回应付账款列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if stock_ins %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        以下入库单已完成财务确认，可以生成应付账款。请选择需要生成应付账款的入库单。
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>入库单号</th>
                                    <th>供应商</th>
                                    <th>入库日期</th>
                                    <th>总金额</th>
                                    <th>采购订单</th>
                                    <th>财务确认时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock_in in stock_ins %}
                                <tr>
                                    <td>{{ stock_in.stock_in_number }}</td>
                                    <td>{{ stock_in.supplier.name if stock_in.supplier else '未知供应商' }}</td>
                                    <td>{{ stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else '未知' }}</td>
                                    <td class="text-right">{{ "%.2f"|format(stock_in.total_cost) }}</td>
                                    <td>{{ stock_in.purchase_order.order_number if stock_in.purchase_order else '-' }}</td>
                                    <td>{{ stock_in.financial_confirmed_at.strftime('%Y-%m-%d %H:%M') if stock_in.financial_confirmed_at else '未知' }}</td>
                                    <td>
                                        <button class="btn btn-success btn-sm" 
                                                onclick="generatePayable({{ stock_in.id }})"
                                                title="生成应付账款">
                                            <i class="fas fa-plus"></i> 生成应付账款
                                        </button>
                                        <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" 
                                           class="btn btn-info btn-sm" 
                                           title="查看入库单详情" 
                                           target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="font-weight-bold">
                                    <td colspan="3" class="text-right">合计金额：</td>
                                    <td class="text-right">{{ "%.2f"|format(stock_ins|sum(attribute='total_cost')) }}</td>
                                    <td colspan="3"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="batchGenerate()">
                            <i class="fas fa-layer-group"></i> 批量生成应付账款
                        </button>
                    </div>
                    {% else %}
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle"></i> 
                        所有已财务确认的入库单都已生成应付账款，暂无待处理的入库单。
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generatePayable(stockInId) {
    if (confirm('确定要为此入库单生成应付账款吗？')) {
        fetch('{{ url_for("financial.generate_payable_from_stock_in") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                stock_in_id: stockInId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('应付账款生成成功！');
                location.reload();
            } else {
                alert('生成失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}

function batchGenerate() {
    if (confirm('确定要为所有显示的入库单批量生成应付账款吗？')) {
        const stockInIds = [];
        {% for stock_in in stock_ins %}
        stockInIds.push({{ stock_in.id }});
        {% endfor %}
        
        let completed = 0;
        let failed = 0;
        
        function generateNext(index) {
            if (index >= stockInIds.length) {
                alert(`批量生成完成！成功：${completed}，失败：${failed}`);
                location.reload();
                return;
            }
            
            fetch('{{ url_for("financial.generate_payable_from_stock_in") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    stock_in_id: stockInIds[index]
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    completed++;
                } else {
                    failed++;
                    console.error('Failed for stock_in_id:', stockInIds[index], data.message);
                }
                generateNext(index + 1);
            })
            .catch(error => {
                failed++;
                console.error('Error for stock_in_id:', stockInIds[index], error);
                generateNext(index + 1);
            });
        }
        
        generateNext(0);
    }
}
</script>
{% endblock %}
