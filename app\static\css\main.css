@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer components {
  /* 这里可以添加自定义组件样式 */
}

/* 全局表头样式优化 */
.table th,
.table thead th,
.table-sm thead th,
.table-bordered thead th,
.table-striped thead th,
.table-hover thead th,
.thead-dark th,
.thead-light th,
table thead th,
table th {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    color: #6c757d !important;
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    text-transform: none !important;
    letter-spacing: normal !important;
}

/* 深色表头保持原有颜色 */
.thead-dark th {
    background-color: #343a40 !important;
    color: #fff !important;
    border-color: #454d55 !important;
}

/* 表格行样式优化 */
.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}