msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: zh-CN\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "错误"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "时间"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "账户"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "收款人"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "损益表"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "资产负债表"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "试算表"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "日记账"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "查询"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "资产"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "通货"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "事件"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "编辑器"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "选项"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "统计"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "帮助"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "日期"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "F"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "价格"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "成本"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "变动"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "余额"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "保存"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "正在保存…"

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "检测到文件发生变化，点击此处刷新。"

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "展开所有账户"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "展开所有"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "账户日记账"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "变更"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "余额"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "源文件 %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "文件"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "行"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "错误"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "没有错误。"

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "事件：%(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "描述"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "帮助页"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "货币"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "成本货币"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "按"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "净利润"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "收入"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "支出"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "键"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "值"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "下载为"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "每账户记录数"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "合计"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "变更动态"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "单击来复制账户（绿色除外）对应的 balance 指令到剪贴板。"

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "复制 balance 指令"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "最新条目"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "每类型条目数"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "类型"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "# 条目"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "摘要"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "数额"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "导入"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "预算条目"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "来源"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "导出"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "请输入一条 BQL 查询命令。输入“help”查看所有可用命令。"

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "元数据"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "添加元数据"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "备注"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "位置"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "内容"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "在此条目前余额"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "在此条目后余额"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "已入账交易"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "未入账交易"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "其他交易"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "带有 #discovered 标签的凭证票据"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "记录"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "上传文件"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "上传"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "对齐金额"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "设为注释"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "展开所有行"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "折叠所有行"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Fava 选项"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "帮助"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Beancount 选项"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "提交"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "按住 Shift 键展开所有子集\n"
"按住 Ctrl 或 Cmd 键并点击展开单层子集"

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "导出"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "筛选（标签，收款人等等）"

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "将当前筛选的条目下载为 Beancount 文件"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "按年"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "按季"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "按月"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "按周"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "按日"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "编辑"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "金额"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "有 #linked 标签的凭证票据"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "按成本"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "按市价"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "按单位数量"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "树状图"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "旭日图"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "该账户未找到"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "上一个"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "下一个"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "交易"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "添加"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "移动或重命名凭证文件"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "名称"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "凭证文档"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "删除"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "折线图"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "面积图"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "继续"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "清除"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "前往账户"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr "是否删除此文件？"

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr "没有发现可供导入的文件。"

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr "切换 %(type)s 条目"

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr "转换为 %(currency)s"

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr "堆叠条形图"

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr "条形图"

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr "图标为空"

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr "切换描述数据"

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr "切换记录"

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr "净值"

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr "账户余额"

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr "重新加载"

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr "继续"

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr "正在删除..."

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr "添加日记账条目"

