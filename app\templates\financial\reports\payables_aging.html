{% extends "financial/base.html" %}

{% block title %}应付账款账龄分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">应付账款账龄分析</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.reports_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回报表列表
                        </a>
                        <button class="btn btn-success btn-sm" onclick="exportReport()">
                            <i class="fas fa-download"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 报表参数 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="analysis_date">分析日期</label>
                                    <input type="date" class="form-control" id="analysis_date" name="analysis_date" 
                                           value="{{ analysis_date }}" onchange="this.form.submit()">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="supplier_id">供应商</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id" onchange="this.form.submit()">
                                        <option value="">-- 所有供应商 --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-sync"></i> 刷新报表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 账龄汇总 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>账龄汇总</h5>
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>账龄区间</th>
                                        <th class="text-right">金额</th>
                                        <th class="text-right">占比</th>
                                        <th class="text-right">供应商数量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>30天以内</td>
                                        <td class="text-right">{{ "%.2f"|format(aging_summary.within_30) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(aging_summary.within_30_percent) }}%</td>
                                        <td class="text-right">{{ aging_summary.within_30_suppliers }}</td>
                                    </tr>
                                    <tr>
                                        <td>31-60天</td>
                                        <td class="text-right">{{ "%.2f"|format(aging_summary.days_31_60) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(aging_summary.days_31_60_percent) }}%</td>
                                        <td class="text-right">{{ aging_summary.days_31_60_suppliers }}</td>
                                    </tr>
                                    <tr>
                                        <td>61-90天</td>
                                        <td class="text-right">{{ "%.2f"|format(aging_summary.days_61_90) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(aging_summary.days_61_90_percent) }}%</td>
                                        <td class="text-right">{{ aging_summary.days_61_90_suppliers }}</td>
                                    </tr>
                                    <tr>
                                        <td>91-180天</td>
                                        <td class="text-right">{{ "%.2f"|format(aging_summary.days_91_180) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(aging_summary.days_91_180_percent) }}%</td>
                                        <td class="text-right">{{ aging_summary.days_91_180_suppliers }}</td>
                                    </tr>
                                    <tr>
                                        <td>180天以上</td>
                                        <td class="text-right">{{ "%.2f"|format(aging_summary.over_180) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(aging_summary.over_180_percent) }}%</td>
                                        <td class="text-right">{{ aging_summary.over_180_suppliers }}</td>
                                    </tr>
                                    <tr class="table-info">
                                        <td><strong>合计</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(aging_summary.total_amount) }}</strong></td>
                                        <td class="text-right"><strong>100.0%</strong></td>
                                        <td class="text-right"><strong>{{ aging_summary.total_suppliers }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 供应商明细 -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>供应商明细</h5>
                            {% if payables_detail %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>供应商</th>
                                            <th>应付账款号</th>
                                            <th class="text-right">原始金额</th>
                                            <th class="text-right">余额</th>
                                            <th class="text-right">账龄（天）</th>
                                            <th>账龄区间</th>
                                            <th>创建日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payable in payables_detail %}
                                        <tr>
                                            <td>{{ payable.supplier_name }}</td>
                                            <td>{{ payable.payable_number }}</td>
                                            <td class="text-right">{{ "%.2f"|format(payable.original_amount) }}</td>
                                            <td class="text-right">{{ "%.2f"|format(payable.balance_amount) }}</td>
                                            <td class="text-right">{{ payable.aging_days }}</td>
                                            <td>
                                                {% if payable.aging_days <= 30 %}
                                                    <span class="badge badge-success">30天以内</span>
                                                {% elif payable.aging_days <= 60 %}
                                                    <span class="badge badge-info">31-60天</span>
                                                {% elif payable.aging_days <= 90 %}
                                                    <span class="badge badge-warning">61-90天</span>
                                                {% elif payable.aging_days <= 180 %}
                                                    <span class="badge badge-danger">91-180天</span>
                                                {% else %}
                                                    <span class="badge badge-dark">180天以上</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ payable.created_at }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i> 暂无应付账款数据
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 报表说明 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 报表说明</h6>
                                <ul class="mb-0">
                                    <li>分析日期：{{ analysis_date }}</li>
                                    <li>编制单位：{{ user_area.name }}</li>
                                    <li>金额单位：人民币元</li>
                                    <li>账龄计算基准：从应付账款创建日期到分析日期</li>
                                    <li>只统计未付清的应付账款</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const analysisDate = document.getElementById('analysis_date').value;
    const supplierId = document.getElementById('supplier_id').value;
    const url = `{{ url_for('financial.export_report', report_type='payables_aging') }}?analysis_date=${analysisDate}&supplier_id=${supplierId}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
