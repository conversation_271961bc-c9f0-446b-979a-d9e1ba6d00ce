# PowerShell脚本：测试xiaoyuanst.com域名访问
# 用于验证域名配置是否正确

Write-Host "=== 测试xiaoyuanst.com域名配置 ===" -ForegroundColor Green
Write-Host "服务器IP: **************" -ForegroundColor Cyan
Write-Host "域名: xiaoyuanst.com" -ForegroundColor Cyan

Write-Host "`n=== 第一步：检查IIS配置 ===" -ForegroundColor Yellow

try {
    Import-Module WebAdministration -Force
    
    # 检查站点状态
    $site = Get-Website -Name "xiaoyuanst.com" -ErrorAction SilentlyContinue
    if ($site) {
        Write-Host "✓ IIS站点存在: xiaoyuanst.com" -ForegroundColor Green
        Write-Host "  状态: $($site.State)" -ForegroundColor White
        Write-Host "  端口: $($site.Bindings.Collection.bindingInformation)" -ForegroundColor White
        Write-Host "  物理路径: $($site.PhysicalPath)" -ForegroundColor White
    } else {
        Write-Host "✗ IIS站点不存在: xiaoyuanst.com" -ForegroundColor Red
    }
    
    # 检查应用程序池状态
    $appPool = Get-WebAppPool -Name "xiaoyuanst.com_AppPool" -ErrorAction SilentlyContinue
    if ($appPool) {
        Write-Host "✓ 应用程序池存在: xiaoyuanst.com_AppPool" -ForegroundColor Green
        Write-Host "  状态: $($appPool.State)" -ForegroundColor White
    } else {
        Write-Host "✗ 应用程序池不存在: xiaoyuanst.com_AppPool" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ 无法检查IIS配置: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 第二步：检查端口占用 ===" -ForegroundColor Yellow

# 检查端口80
$port80 = netstat -ano | findstr ":80 "
if ($port80) {
    Write-Host "✓ 端口80正在使用中" -ForegroundColor Green
    Write-Host "  详情: $($port80[0])" -ForegroundColor White
} else {
    Write-Host "✗ 端口80未被占用" -ForegroundColor Red
}

# 检查端口8080
$port8080 = netstat -ano | findstr ":8080 "
if ($port8080) {
    Write-Host "✓ 端口8080正在使用中（Flask应用）" -ForegroundColor Green
    Write-Host "  详情: $($port8080[0])" -ForegroundColor White
} else {
    Write-Host "✗ 端口8080未被占用（Flask应用未启动）" -ForegroundColor Red
}

Write-Host "`n=== 第三步：检查防火墙规则 ===" -ForegroundColor Yellow

try {
    $firewallRules = Get-NetFirewallRule -DisplayName "*xiaoyuanst*" -ErrorAction SilentlyContinue
    if ($firewallRules) {
        Write-Host "✓ 找到xiaoyuanst.com相关防火墙规则:" -ForegroundColor Green
        foreach ($rule in $firewallRules) {
            Write-Host "  - $($rule.DisplayName): $($rule.Action)" -ForegroundColor White
        }
    } else {
        Write-Host "✗ 未找到xiaoyuanst.com相关防火墙规则" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 无法检查防火墙规则: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 第四步：检查web.config文件 ===" -ForegroundColor Yellow

$webConfigPath = "C:\StudentsCMSSP\web.config"
if (Test-Path $webConfigPath) {
    Write-Host "✓ web.config文件存在" -ForegroundColor Green
    
    # 检查是否包含正确的代理配置
    $webConfigContent = Get-Content $webConfigPath -Raw
    if ($webConfigContent -match "127\.0\.0\.1:8080") {
        Write-Host "✓ web.config包含正确的代理配置（端口8080）" -ForegroundColor Green
    } else {
        Write-Host "✗ web.config代理配置可能不正确" -ForegroundColor Red
    }
} else {
    Write-Host "✗ web.config文件不存在于: $webConfigPath" -ForegroundColor Red
}

Write-Host "`n=== 第五步：测试本地连接 ===" -ForegroundColor Yellow

try {
    # 测试Flask应用直接访问
    Write-Host "测试Flask应用直接访问（端口8080）..." -ForegroundColor White
    $response8080 = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($response8080.StatusCode -eq 200) {
        Write-Host "✓ Flask应用响应正常（端口8080）" -ForegroundColor Green
    } else {
        Write-Host "✗ Flask应用无响应（端口8080）" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Flask应用连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    # 测试IIS代理访问
    Write-Host "测试IIS代理访问（端口80）..." -ForegroundColor White
    $response80 = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($response80.StatusCode -eq 200) {
        Write-Host "✓ IIS代理响应正常（端口80）" -ForegroundColor Green
    } else {
        Write-Host "✗ IIS代理无响应（端口80）" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ IIS代理连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试总结 ===" -ForegroundColor Green
Write-Host "如果所有测试都通过，您可以尝试以下访问方式：" -ForegroundColor White
Write-Host "1. 本地访问: http://localhost" -ForegroundColor Cyan
Write-Host "2. IP访问: http://**************" -ForegroundColor Cyan
Write-Host "3. 域名访问: http://xiaoyuanst.com" -ForegroundColor Cyan
Write-Host "4. 带www的域名访问: http://www.xiaoyuanst.com" -ForegroundColor Cyan

Write-Host "`n=== 故障排除建议 ===" -ForegroundColor Yellow
Write-Host "如果测试失败，请检查：" -ForegroundColor White
Write-Host "1. 确保以管理员身份运行setup_xiaoyuanst_domain.ps1" -ForegroundColor White
Write-Host "2. 确保Flask应用正在运行：python run.py" -ForegroundColor White
Write-Host "3. 确保域名DNS记录指向服务器IP: **************" -ForegroundColor White
Write-Host "4. 检查服务器防火墙和网络配置" -ForegroundColor White
Write-Host "5. 重启IIS服务：iisreset" -ForegroundColor White
