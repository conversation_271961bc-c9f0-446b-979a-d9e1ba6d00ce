2025-06-07 21:52:47,649 INFO: 应用启动 - PID: 26192 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-07 21:53:02,880 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 21:53:02,901 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 21:53:16,773 ERROR: 创建财务凭证失败: local variable 'FinancialVoucher' referenced before assignment [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:133]
2025-06-07 21:53:17,320 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 21:53:17,346 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 21:53:31,450 ERROR: 创建财务凭证失败: local variable 'FinancialVoucher' referenced before assignment [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:133]
2025-06-07 21:55:04,990 INFO: 查询菜谱：日期=2025-06-07, 星期=5(0=周一), day_of_week=6, 餐次=午餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-07 21:55:04,993 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-07 21:55:04,997 INFO: 匹配条件的食谱有 1 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-07 21:55:05,008 INFO:   - 食谱: 鲜蚕豆烧大雁（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-07 21:55:05,013 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-07 21:55:17,053 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-07 21:55:19,875 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-07 21:57:01,060 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 21:57:01,060 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 21:57:01,062 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 21:57:01,062 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 21:57:01,067 ERROR: 获取消耗计划列表失败: cannot unpack non-iterable ConsumptionPlan object [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:275]
2025-06-07 21:57:41,293 INFO: 使用消耗计划的区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-07 21:57:41,293 INFO: 通过消耗计划信息读取菜谱：日期=2024-12-20, 餐次=晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-07 21:57:41,294 INFO: 查询周菜单：日期=2024-12-20, 星期=4(0=周一), day_of_week=5, 餐次=晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-07 21:57:41,295 INFO: 找到 0 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-07 21:57:41,295 INFO: 未找到匹配的周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:252]
2025-06-07 21:57:41,295 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-07 21:57:41,300 INFO: 步骤1: 读取消耗日期: 2024-12-20, 餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-07 21:57:41,304 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-07 21:57:41,306 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 21:57:41,307 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 21:57:41,307 INFO: 步骤2: 为出库食材 '牛肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-07 21:57:41,307 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-07 21:58:27,873 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-07 21:58:30,406 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-07 21:58:33,938 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-07 21:59:02,771 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-07 21:59:02,772 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-07 21:59:02,772 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-07 21:59:02,772 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-07 21:59:02,774 ERROR: 获取消耗计划列表失败: cannot unpack non-iterable ConsumptionPlan object [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:275]
