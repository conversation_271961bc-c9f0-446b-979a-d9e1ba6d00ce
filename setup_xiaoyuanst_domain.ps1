# PowerShell脚本：配置xiaoyuanst.com域名访问
# 需要以管理员身份运行
# 服务器IP: **************
# 域名: xiaoyuanst.com
# 端口: 8080

Write-Host "正在配置xiaoyuanst.com域名访问..." -ForegroundColor Green
Write-Host "服务器IP: **************" -ForegroundColor Cyan
Write-Host "域名: xiaoyuanst.com" -ForegroundColor Cyan
Write-Host "Flask端口: 8080" -ForegroundColor Cyan
Write-Host "IIS端口: 80" -ForegroundColor Cyan

# 确保IIS模块正确加载
try {
    Import-Module WebAdministration -Force
    Write-Host "WebAdministration模块加载成功" -ForegroundColor Green
} catch {
    Write-Host "WebAdministration模块加载失败，尝试启用IIS功能..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing -All
    Import-Module WebAdministration -Force
}

# 站点配置参数
$siteName = "xiaoyuanst.com"
$sitePort = 80
$sitePath = "C:\StudentsCMSSP"
$appPoolName = "xiaoyuanst.com_AppPool"

try {
    Write-Host "`n=== 第一步：安装URL Rewrite模块 ===" -ForegroundColor Yellow

    # 检查URL Rewrite模块是否安装
    $urlRewriteModule = Get-WebGlobalModule -Name "RewriteModule" -ErrorAction SilentlyContinue
    if ($urlRewriteModule) {
        Write-Host "✓ URL Rewrite模块已安装" -ForegroundColor Green
    } else {
        Write-Host "正在下载并安装URL Rewrite模块..." -ForegroundColor Yellow

        # 下载并安装URL Rewrite模块
        $url = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
        $output = "$env:TEMP\rewrite_amd64_en-US.msi"

        try {
            Invoke-WebRequest -Uri $url -OutFile $output
            Start-Process msiexec.exe -Wait -ArgumentList "/i $output /quiet"
            Remove-Item $output -Force
            Write-Host "✓ URL Rewrite模块安装完成" -ForegroundColor Green

            # 重启IIS以加载模块
            iisreset /noforce
            Write-Host "✓ IIS服务已重启" -ForegroundColor Green
        } catch {
            Write-Host "✗ URL Rewrite模块安装失败: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "请手动下载并安装URL Rewrite模块" -ForegroundColor Yellow
        }
    }

    Write-Host "`n=== 第二步：配置IIS站点 ===" -ForegroundColor Yellow
    
    # 1. 创建应用程序池
    Write-Host "创建应用程序池: $appPoolName" -ForegroundColor Yellow
    
    # 检查应用程序池是否存在
    $existingPool = Get-ChildItem IIS:\AppPools | Where-Object { $_.Name -eq $appPoolName }
    if ($existingPool) {
        Remove-WebAppPool -Name $appPoolName
        Write-Host "已删除现有应用程序池" -ForegroundColor Yellow
    }
    
    New-WebAppPool -Name $appPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "enable32BitAppOnWin64" -Value $false
    
    Write-Host "应用程序池创建完成" -ForegroundColor Green
    
    # 2. 删除默认网站（如果存在）
    if (Get-Website -Name "Default Web Site" -ErrorAction SilentlyContinue) {
        Remove-Website -Name "Default Web Site"
        Write-Host "已删除默认网站" -ForegroundColor Yellow
    }
    
    # 3. 删除现有站点（如果存在）
    if (Get-Website -Name $siteName -ErrorAction SilentlyContinue) {
        Remove-Website -Name $siteName
        Write-Host "已删除现有站点" -ForegroundColor Yellow
    }
    
    # 4. 创建新站点
    Write-Host "创建站点: $siteName" -ForegroundColor Yellow
    New-Website -Name $siteName -Port $sitePort -PhysicalPath $sitePath -ApplicationPool $appPoolName
    
    # 5. 添加域名绑定
    Write-Host "添加域名绑定..." -ForegroundColor Yellow
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "xiaoyuanst.com"
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "www.xiaoyuanst.com"
    
    # 6. 设置站点权限
    Write-Host "设置站点权限..." -ForegroundColor Yellow
    $acl = Get-Acl $sitePath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    $accessRule2 = New-Object System.Security.AccessControl.FileSystemAccessRule("IUSR", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule2)
    Set-Acl -Path $sitePath -AclObject $acl
    
    # 7. 配置允许的服务器变量（解决URL Rewrite错误）
    Write-Host "配置允许的服务器变量..." -ForegroundColor Yellow
    $serverVariables = @(
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED_PROTO",
        "HTTP_X_FORWARDED_HOST",
        "HTTP_X_REAL_IP"
    )

    foreach ($variable in $serverVariables) {
        try {
            Add-WebConfigurationProperty -PSPath "IIS:\Sites\$siteName" -Filter "system.webServer/rewrite/allowedServerVariables" -Name "." -Value @{name=$variable}
            Write-Host "✓ 已添加允许的服务器变量: $variable" -ForegroundColor Green
        } catch {
            # 如果变量已存在，会抛出异常，这是正常的
            Write-Host "- 服务器变量已存在: $variable" -ForegroundColor Gray
        }
    }

    # 8. 启动站点和应用程序池
    Write-Host "启动站点和应用程序池..." -ForegroundColor Yellow
    Start-WebAppPool -Name $appPoolName
    Start-Website -Name $siteName
    
    Write-Host "`n=== 第三步：配置防火墙 ===" -ForegroundColor Yellow
    
    # 删除可能存在的旧规则
    $oldRules = @("HTTP Port 80", "Flask App Port 80", "IIS HTTP", "tdtech.xin HTTP", "xiaoyuanst.com HTTP")
    foreach ($rule in $oldRules) {
        try {
            Remove-NetFirewallRule -DisplayName $rule -ErrorAction SilentlyContinue
        } catch {
            # 忽略不存在的规则
        }
    }
    
    # 添加防火墙规则
    New-NetFirewallRule -DisplayName "xiaoyuanst.com HTTP Inbound" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow -Profile Any
    Write-Host "✓ 已添加HTTP端口80入站规则" -ForegroundColor Green
    
    New-NetFirewallRule -DisplayName "xiaoyuanst.com HTTP Outbound" -Direction Outbound -Protocol TCP -LocalPort 80 -Action Allow -Profile Any
    Write-Host "✓ 已添加HTTP端口80出站规则" -ForegroundColor Green
    
    New-NetFirewallRule -DisplayName "xiaoyuanst.com Flask Local" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow -RemoteAddress LocalSubnet
    Write-Host "✓ 已添加Flask应用本地访问规则" -ForegroundColor Green
    
    New-NetFirewallRule -DisplayName "xiaoyuanst.com HTTPS Inbound" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow -Profile Any
    Write-Host "✓ 已添加HTTPS端口443规则" -ForegroundColor Green
    
    Write-Host "`n=== 配置完成 ===" -ForegroundColor Green
    Write-Host "站点名称: $siteName" -ForegroundColor Cyan
    Write-Host "端口: $sitePort" -ForegroundColor Cyan
    Write-Host "物理路径: $sitePath" -ForegroundColor Cyan
    Write-Host "应用程序池: $appPoolName" -ForegroundColor Cyan
    Write-Host "域名绑定: xiaoyuanst.com, www.xiaoyuanst.com" -ForegroundColor Cyan
    
} catch {
    Write-Host "配置过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 下一步操作 ===" -ForegroundColor Yellow
Write-Host "1. 确保Flask应用正在端口8080上运行：python run.py" -ForegroundColor White
Write-Host "2. 确保域名DNS记录指向服务器IP: **************" -ForegroundColor White
Write-Host "3. 测试本地访问：http://localhost" -ForegroundColor White
Write-Host "4. 测试域名访问：http://xiaoyuanst.com" -ForegroundColor White
Write-Host "5. 检查web.config文件是否存在并配置正确" -ForegroundColor White

Write-Host "`n=== 架构说明 ===" -ForegroundColor Yellow
Write-Host "外网用户 → xiaoyuanst.com:80 → IIS → Flask应用:8080" -ForegroundColor White
