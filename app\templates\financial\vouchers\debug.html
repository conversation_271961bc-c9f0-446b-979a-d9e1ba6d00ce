{% extends "financial/base.html" %}

{% block title %}凭证创建调试{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">凭证创建调试</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 调试信息 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>系统信息</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong>用户ID</strong></td>
                                        <td>{{ debug_info.user_id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>学校区域ID</strong></td>
                                        <td>{{ debug_info.user_area_id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>学校名称</strong></td>
                                        <td>{{ debug_info.user_area_name }}</td>
                                    </tr>
                                    {% if debug_info.database_test %}
                                    <tr>
                                        <td><strong>数据库测试</strong></td>
                                        <td>{{ debug_info.database_test }}</td>
                                    </tr>
                                    {% endif %}
                                    {% if debug_info.voucher_number %}
                                    <tr>
                                        <td><strong>生成的凭证号</strong></td>
                                        <td>{{ debug_info.voucher_number }}</td>
                                    </tr>
                                    {% endif %}
                                    {% if debug_info.success %}
                                    <tr>
                                        <td><strong>创建结果</strong></td>
                                        <td class="text-success">成功！凭证ID: {{ debug_info.voucher_id }}</td>
                                    </tr>
                                    {% endif %}
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 表单验证信息 -->
                    {% if debug_info.form_validation %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>表单验证信息</h5>
                            <div class="alert {% if debug_info.form_validation.is_valid %}alert-success{% else %}alert-danger{% endif %}">
                                <strong>验证结果：</strong>
                                {% if debug_info.form_validation.is_valid %}
                                    通过
                                {% else %}
                                    失败
                                {% endif %}
                            </div>
                            
                            {% if debug_info.form_validation.errors %}
                            <div class="alert alert-warning">
                                <strong>验证错误：</strong>
                                <ul class="mb-0">
                                    {% for field, errors in debug_info.form_validation.errors.items() %}
                                        {% for error in errors %}
                                        <li>{{ field }}: {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                            
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong>凭证日期</strong></td>
                                        <td>{{ debug_info.form_validation.data.voucher_date }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>凭证类型</strong></td>
                                        <td>{{ debug_info.form_validation.data.voucher_type }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>摘要</strong></td>
                                        <td>{{ debug_info.form_validation.data.summary }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>备注</strong></td>
                                        <td>{{ debug_info.form_validation.data.notes or '无' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 错误详情 -->
                    {% if debug_info.error_details %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>错误详情</h5>
                            <div class="alert alert-danger">
                                <strong>错误消息：</strong>{{ debug_info.error_details.error_message }}
                            </div>
                            <div class="card">
                                <div class="card-header">
                                    <h6>详细堆栈跟踪</h6>
                                </div>
                                <div class="card-body">
                                    <pre style="font-size: 12px; max-height: 300px; overflow-y: auto;">{{ debug_info.error_details.traceback }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 测试表单 -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>测试凭证创建</h5>
                            <form method="POST">
                                {{ form.hidden_tag() }}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.voucher_date.label(class="form-label") }}
                                            {{ form.voucher_date(class="form-control") }}
                                            {% if form.voucher_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.voucher_date.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.voucher_type.label(class="form-label") }}
                                            {{ form.voucher_type(class="form-control") }}
                                            {% if form.voucher_type.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.voucher_type.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    {{ form.summary.label(class="form-label") }}
                                    {{ form.summary(class="form-control", placeholder="请输入凭证摘要") }}
                                    {% if form.summary.errors %}
                                        <div class="text-danger">
                                            {% for error in form.summary.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="3", placeholder="可选的备注信息") }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger">
                                            {% for error in form.notes.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-bug"></i> 调试创建凭证
                                    </button>
                                    <a href="{{ url_for('financial.create_voucher') }}" class="btn btn-success">
                                        <i class="fas fa-plus"></i> 正常创建凭证
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
