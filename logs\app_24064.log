2025-06-07 21:44:37,291 INFO: 应用启动 - PID: 24064 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-07 21:44:50,758 ERROR: Exception on /financial/payables/pending-stock-ins [GET] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.ProgrammingError: ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'area_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'area_id' 无效。 (207); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\school_required.py", line 34, in decorated_function
    return f(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\permissions.py", line 574, in decorated_function
    return f(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\payables.py", line 263, in pending_stock_ins
    stock_ins = StockIn.query.filter(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2673, in all
    return self._iter().all()  # type: ignore
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (pyodbc.ProgrammingError) ('42S22', "[42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'area_id' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'supplier_id' 无效。 (207); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]列名 'area_id' 无效。 (207); [42S22] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT stock_ins.id AS stock_ins_id, stock_ins.stock_in_number AS stock_ins_stock_in_number, stock_ins.warehouse_id AS stock_ins_warehouse_id, stock_ins.delivery_id AS stock_ins_delivery_id, stock_ins.purchase_order_id AS stock_ins_purchase_order_id, stock_ins.stock_in_date AS stock_ins_stock_in_date, stock_ins.stock_in_type AS stock_ins_stock_in_type, stock_ins.operator_id AS stock_ins_operator_id, stock_ins.inspector_id AS stock_ins_inspector_id, stock_ins.status AS stock_ins_status, stock_ins.notes AS stock_ins_notes, stock_ins.is_financial_confirmed AS stock_ins_is_financial_confirmed, stock_ins.total_cost AS stock_ins_total_cost, stock_ins.supplier_id AS stock_ins_supplier_id, stock_ins.payable_id AS stock_ins_payable_id, stock_ins.voucher_id AS stock_ins_voucher_id, stock_ins.area_id AS stock_ins_area_id, stock_ins.created_at AS stock_ins_created_at, stock_ins.updated_at AS stock_ins_updated_at 
FROM stock_ins 
WHERE stock_ins.area_id = ? AND stock_ins.is_financial_confirmed = 1 AND stock_ins.payable_id IS NULL ORDER BY stock_ins.created_at DESC]
[parameters: (42,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
