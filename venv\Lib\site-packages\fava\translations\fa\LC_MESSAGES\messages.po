msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: fa\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "خطا‌ها"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "زمان"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "حساب‌ کاربری"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "دریافت کننده"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "صورت حساب درآمد"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "ترازنامه"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "تراز‌نامه ‌آزمایشی"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "مجله"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "پرس ‌و ‌جو"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "دارایی‌ ها"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "کالا ها"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "رویداد ها"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "ویرایشگر"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "گزینه‌ ها"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "آمار"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "راهنما"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "تاریخ"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "F"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "قیمت"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "قیمت"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "تغییر"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "تراز"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "ذخیره"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "پس انداز..."

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "تغییر‌فایل‌شناسایی‌شده‌است. برای‌بارگیری‌مجدد‌کلیک‌کنید."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "توسعه‌حساب‌ها"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "توسعه‌همه"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "مجله‌حساب"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "تغییرات"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "ترازها"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "نمایش منبع %(فایل)ها:%(شماره خط)ها"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "فایل"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "خط"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "خطا"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "بدون خطا."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "رویداد: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "توضیحات"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "صفحات‌راهنما"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "واحد‌ارز"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "قیمت ارز"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "منابع‌توسط"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "سود‌خالص"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "درآمد"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "هزینه‌ها"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "کلید"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "مقدار"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "دانلود‌به‌عنوان"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "پست‌برای‌هر‌حساب"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "کل"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "بروزرسانی‌فعالیت"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "برای‌کپی‌کردن‌دستورات تراز‌ها‌‌برای‌حساب‌ها‌(به‌جزموارد‌سبز)درکلیپ‌بور‌کلیک‌کنید."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "کپی‌دستورات‌ترازها"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "آخرین‌ورودی"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "ورودی‌ها‌درهرنوع"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "نوع"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "# ورودی‌ها"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "توصیف"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "شماره"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "واردکردن"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "ورودی‌های‌بودجه"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "منبع"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "استخراج"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "... یک‌پرس‌وجوBQLواردکنید.'راهنما'برای‌فهرست‌دستورات‌موجود."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "متاداده"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "افزودن‌متاداده"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "یادداشت"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "محل"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "متن‌"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "ترازقبل‌ازورود"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "ترازبعدازورود"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "تراکنش‌های‌پاک‌شده"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "تراکنش‌های‌درحال‌انتظار"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "سایر‌تراکنش‌ها"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "اسناد‌با‌برچسب#کشف‌شده"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "پست‌ها"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "آپلودفایل‌(ها)"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "آپلود"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "هماهنگ‌کردن‌تعداد"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "تغییر‌نظر(انتخاب)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "بازکردن‌همه‌چینه‌ها"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "بستن‌همه‌چینه‌ها"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "گزینه‌های‌فاوا"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "راهنما"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "گزینه‌های‌شمارش‌جزئی"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "ارسال"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "نگه‌داشتن Shiftوکلیک‌کردن‌برای‌گسترش‌تمام‌سطح‌ها.\n"
"نگه‌داشتن Ctrlیا Cmdوکلیک‌کردن‌برای‌گسترش‌یک‌سطح."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "صادر‌کردن"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "فیلتربراساس برچسب،پرداخت‌کننده، ..."

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "دریافت‌فایل‌شمارش‌جزئی‌فیلترشده‌برای‌وودی‌فعلی"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "سالانه"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "سه‌ماهه"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "ماهانه"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "هفتگی"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "روزانه"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "ویرایش"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "میزان"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "اسناد با برچسب # مرتبط شده"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "با قیمت"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "در ارزش بازار"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "واحدها"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "نمودار درختی"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "نمودار دایره‌ای"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "باید یکی از حساب های اعلام شده باشد"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "قبلی"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "بعدی"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "تراکنش"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "افزودن"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "انتقال یا تغییر نام سند"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "نام"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "اسناد"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "حذف"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "نمودار خطی"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "نمودار ناحیه ای"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "ادامه"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "پاک کردن"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "رفتن به حساب"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr ""

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr ""

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr ""

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr ""

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr ""

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr ""

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr ""

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr ""

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

