"""
财务计算工具模块
集成 beancount、pandas 等专业财务库
"""

import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Tuple
import logging

# Beancount 相关导入
try:
    from beancount.core import data
    from beancount.core.amount import Amount
    from beancount.core.number import D
    BEANCOUNT_AVAILABLE = True
except ImportError:
    BEANCOUNT_AVAILABLE = False
    logging.warning("Beancount 未安装，将使用简化的借贷平衡验证")

logger = logging.getLogger(__name__)


class FinancialCalculator:
    """财务计算器 - 集成专业财务计算功能"""
    
    @staticmethod
    def validate_voucher_balance(details: List[Dict[str, Any]], tolerance: Decimal = Decimal('0.01')) -> Tuple[bool, str]:
        """
        验证凭证借贷平衡
        
        Args:
            details: 凭证明细列表，每个明细包含 debit_amount 和 credit_amount
            tolerance: 允许的误差范围，默认 0.01 元
            
        Returns:
            (是否平衡, 错误信息)
        """
        try:
            total_debit = Decimal('0')
            total_credit = Decimal('0')
            
            for detail in details:
                debit = Decimal(str(detail.get('debit_amount', 0) or 0))
                credit = Decimal(str(detail.get('credit_amount', 0) or 0))
                
                # 验证单个明细的合理性
                if debit < 0 or credit < 0:
                    return False, f"金额不能为负数: 借方{debit}, 贷方{credit}"
                
                if debit > 0 and credit > 0:
                    return False, f"借方和贷方不能同时有金额: 借方{debit}, 贷方{credit}"
                
                if debit == 0 and credit == 0:
                    return False, "借方和贷方不能同时为零"
                
                total_debit += debit
                total_credit += credit
            
            # 检查借贷平衡
            difference = abs(total_debit - total_credit)
            if difference > tolerance:
                return False, f"借贷不平衡: 借方合计{total_debit}, 贷方合计{total_credit}, 差额{difference}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证凭证平衡时出错: {e}")
            return False, f"验证失败: {str(e)}"
    
    @staticmethod
    def calculate_account_balance(transactions: List[Dict[str, Any]], 
                                account_code: str, 
                                balance_direction: str = 'debit') -> Decimal:
        """
        计算科目余额
        
        Args:
            transactions: 交易记录列表
            account_code: 科目编码
            balance_direction: 余额方向 ('debit' 或 'credit')
            
        Returns:
            科目余额
        """
        try:
            total_debit = Decimal('0')
            total_credit = Decimal('0')
            
            for trans in transactions:
                if trans.get('account_code') == account_code:
                    total_debit += Decimal(str(trans.get('debit_amount', 0) or 0))
                    total_credit += Decimal(str(trans.get('credit_amount', 0) or 0))
            
            if balance_direction.lower() == 'debit':
                return total_debit - total_credit
            else:
                return total_credit - total_debit
                
        except Exception as e:
            logger.error(f"计算科目余额时出错: {e}")
            return Decimal('0')
    
    @staticmethod
    def round_currency(amount: Decimal, places: int = 2) -> Decimal:
        """
        货币金额四舍五入
        
        Args:
            amount: 金额
            places: 小数位数，默认2位
            
        Returns:
            四舍五入后的金额
        """
        if places == 2:
            return amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        else:
            quantizer = Decimal('0.' + '0' * places)
            return amount.quantize(quantizer, rounding=ROUND_HALF_UP)


class FinancialReportGenerator:
    """财务报表生成器 - 使用 pandas 进行数据分析"""
    
    def __init__(self):
        self.calculator = FinancialCalculator()
    
    def generate_trial_balance(self, voucher_details: List[Dict[str, Any]], 
                             period_start: date, period_end: date) -> pd.DataFrame:
        """
        生成试算平衡表
        
        Args:
            voucher_details: 凭证明细数据
            period_start: 期间开始日期
            period_end: 期间结束日期
            
        Returns:
            试算平衡表 DataFrame
        """
        try:
            # 转换为 DataFrame
            df = pd.DataFrame(voucher_details)
            
            if df.empty:
                return pd.DataFrame(columns=['科目编码', '科目名称', '借方发生额', '贷方发生额', '借方余额', '贷方余额'])
            
            # 数据类型转换
            df['debit_amount'] = pd.to_numeric(df['debit_amount'].fillna(0), errors='coerce')
            df['credit_amount'] = pd.to_numeric(df['credit_amount'].fillna(0), errors='coerce')
            
            # 按科目汇总
            summary = df.groupby(['account_code', 'account_name']).agg({
                'debit_amount': 'sum',
                'credit_amount': 'sum'
            }).reset_index()
            
            # 计算余额
            summary['balance'] = summary['debit_amount'] - summary['credit_amount']
            summary['debit_balance'] = summary['balance'].apply(lambda x: x if x > 0 else 0)
            summary['credit_balance'] = summary['balance'].apply(lambda x: -x if x < 0 else 0)
            
            # 重命名列
            summary.columns = ['科目编码', '科目名称', '借方发生额', '贷方发生额', '余额', '借方余额', '贷方余额']
            summary = summary.drop('余额', axis=1)
            
            # 格式化金额
            for col in ['借方发生额', '贷方发生额', '借方余额', '贷方余额']:
                summary[col] = summary[col].apply(lambda x: f"{x:.2f}")
            
            return summary
            
        except Exception as e:
            logger.error(f"生成试算平衡表时出错: {e}")
            return pd.DataFrame()
    
    def generate_income_statement(self, voucher_details: List[Dict[str, Any]], 
                                period_start: date, period_end: date) -> pd.DataFrame:
        """
        生成利润表（收支表）
        
        Args:
            voucher_details: 凭证明细数据
            period_start: 期间开始日期
            period_end: 期间结束日期
            
        Returns:
            利润表 DataFrame
        """
        try:
            df = pd.DataFrame(voucher_details)
            
            if df.empty:
                return pd.DataFrame(columns=['科目类型', '科目名称', '本期金额', '累计金额'])
            
            # 筛选收入和费用科目
            income_expense_df = df[df['subject_type'].isin(['收入', '费用'])]
            
            if income_expense_df.empty:
                return pd.DataFrame(columns=['科目类型', '科目名称', '本期金额', '累计金额'])
            
            # 数据处理
            income_expense_df['amount'] = pd.to_numeric(
                income_expense_df['credit_amount'].fillna(0) - income_expense_df['debit_amount'].fillna(0), 
                errors='coerce'
            )
            
            # 按科目类型和科目汇总
            summary = income_expense_df.groupby(['subject_type', 'account_name']).agg({
                'amount': 'sum'
            }).reset_index()
            
            # 重命名列
            summary.columns = ['科目类型', '科目名称', '本期金额']
            summary['累计金额'] = summary['本期金额']  # 简化处理，实际应该查询累计数据
            
            # 格式化金额
            summary['本期金额'] = summary['本期金额'].apply(lambda x: f"{x:.2f}")
            summary['累计金额'] = summary['累计金额'].apply(lambda x: f"{x:.2f}")
            
            return summary
            
        except Exception as e:
            logger.error(f"生成利润表时出错: {e}")
            return pd.DataFrame()
    
    def export_to_excel(self, data: pd.DataFrame, filename: str, sheet_name: str = 'Sheet1') -> bool:
        """
        导出数据到 Excel
        
        Args:
            data: 要导出的数据
            filename: 文件名
            sheet_name: 工作表名称
            
        Returns:
            是否导出成功
        """
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info(f"数据已导出到 {filename}")
            return True
            
        except Exception as e:
            logger.error(f"导出Excel时出错: {e}")
            return False


class BeancountIntegration:
    """Beancount 集成类"""
    
    def __init__(self):
        self.available = BEANCOUNT_AVAILABLE
    
    def validate_with_beancount(self, voucher_details: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """
        使用 Beancount 验证凭证
        
        Args:
            voucher_details: 凭证明细
            
        Returns:
            (是否有效, 错误信息)
        """
        if not self.available:
            # 回退到简单验证
            return FinancialCalculator.validate_voucher_balance(voucher_details)
        
        try:
            # 使用 Beancount 的精确计算
            total_debit = D('0')
            total_credit = D('0')
            
            for detail in voucher_details:
                debit = D(str(detail.get('debit_amount', 0) or 0))
                credit = D(str(detail.get('credit_amount', 0) or 0))
                
                total_debit += debit
                total_credit += credit
            
            # Beancount 风格的平衡检查
            if total_debit != total_credit:
                return False, f"借贷不平衡: 借方{total_debit}, 贷方{total_credit}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"Beancount 验证时出错: {e}")
            return False, f"验证失败: {str(e)}"


# 创建全局实例
financial_calculator = FinancialCalculator()
report_generator = FinancialReportGenerator()
beancount_integration = BeancountIntegration()
