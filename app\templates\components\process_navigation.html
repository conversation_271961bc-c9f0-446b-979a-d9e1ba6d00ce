{% macro process_navigation(menu_plan, purchase_plan, storage_in, consumption_plan, storage_out, inventory, samples, tracing, progress_percentage, today_tasks=None, available_routes=[]) %}
<div class="process-navigation-container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 class="section-title mb-0">食堂管理流程</h4>
        <div class="process-filter">
            <button class="btn btn-sm btn-outline-secondary active" data-filter="all">全部</button>
            <button class="btn btn-sm btn-outline-primary" data-filter="in-progress">进行中</button>
            <button class="btn btn-sm btn-outline-success" data-filter="completed">已完成</button>
            <button class="btn btn-sm btn-outline-warning" data-filter="warning">需注意</button>
        </div>
    </div>

    <!-- 流程进度指示器 -->
    <div class="progress mb-3" style="height: 10px;">
        <div class="progress-bar bg-success" role="progressbar" style="width: {{ progress_percentage }}%;"
             aria-valuenow="{{ progress_percentage }}" aria-valuemin="0" aria-valuemax="100">{{ progress_percentage|int }}%</div>
    </div>

    <!-- 流程图主体 - 单行布局 -->
    <div class="process-flow-chart">
        <div class="process-steps">
            <!-- 步骤1：周菜单计划 -->
            <div class="process-step {{ weekly_menu.status if weekly_menu else 'pending' }}">
                <div class="step-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="step-content">
                    <h5>周菜单计划</h5>
                    <p class="step-status">{{ weekly_menu.status_text if weekly_menu else '待制定' }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('main.recipes') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('weekly_menu_v2.plan') }}" class="btn btn-sm btn-success">新建</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceMenuPlan" aria-expanded="false" aria-controls="guidanceMenuPlan">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceMenuPlan">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>根据周菜单制定采购计划</p>
                                <ul class="guidance-tips">
                                    <li>提前一周规划下周菜单</li>
                                    <li>确保菜单营养均衡</li>
                                    <li>考虑季节性食材的可获得性</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤2：采购计划 -->
            <div class="process-step {{ purchase_plan.status }}">
                <div class="step-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="step-content">
                    <h5>采购计划</h5>
                    <p class="step-status">{{ purchase_plan.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-sm btn-success">新建</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidancePurchasePlan" aria-expanded="false" aria-controls="guidancePurchasePlan">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidancePurchasePlan">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>根据采购计划进行食材入库</p>
                                <ul class="guidance-tips">
                                    <li>根据周菜单计算所需食材数量</li>
                                    <li>检查现有库存，避免重复采购</li>
                                    <li>选择合格供应商，确保食材质量</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤4：入库 -->
            <div class="process-step {{ storage_in.status }}">
                <div class="step-icon">
                    <i class="fas fa-dolly-flatbed"></i>
                </div>
                <div class="step-content">
                    <h5>入库</h5>
                    <p class="step-status">{{ storage_in.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('main.ingredients') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('stock_in.create') }}" class="btn btn-sm btn-success">新建</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceStorageIn" aria-expanded="false" aria-controls="guidanceStorageIn">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceStorageIn">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>制定食材消耗计划</p>
                                <ul class="guidance-tips">
                                    <li>按食材类型分类存放</li>
                                    <li>记录入库时间和保质期</li>
                                    <li>检查食材质量和新鲜度</li>
                                    <li>遵循先进先出原则</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤5：消耗量计划 -->
            <div class="process-step {{ consumption_plan.status }}">
                <div class="step-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="step-content">
                    <h5>消耗量计划</h5>
                    <p class="step-status">{{ consumption_plan.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('daily_management.index') }}" class="btn btn-sm btn-success">新建</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceConsumption" aria-expanded="false" aria-controls="guidanceConsumption">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceConsumption">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>按计划进行食材出库</p>
                                <ul class="guidance-tips">
                                    <li>根据菜单和就餐人数计算用量</li>
                                    <li>合理控制食材损耗率</li>
                                    <li>记录实际消耗与计划的差异</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤6：出库 -->
            <div class="process-step {{ storage_out.status }}">
                <div class="step-icon">
                    <i class="fas fa-truck-loading"></i>
                </div>
                <div class="step-content">
                    <h5>出库</h5>
                    <p class="step-status">{{ storage_out.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('stock_out.index') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('stock_out.create') }}" class="btn btn-sm btn-success">新建</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceStorageOut" aria-expanded="false" aria-controls="guidanceStorageOut">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceStorageOut">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>更新库存并进行库存管理</p>
                                <ul class="guidance-tips">
                                    <li>按照先进先出原则出库</li>
                                    <li>检查食材质量和保质期</li>
                                    <li>准确记录出库数量和时间</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤7：库存 -->
            <div class="process-step {{ inventory.status }}">
                <div class="step-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="step-content">
                    <h5>库存</h5>
                    <p class="step-status">{{ inventory.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-info">盘点</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceInventory" aria-expanded="false" aria-controls="guidanceInventory">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceInventory">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>进行食品留样记录</p>
                                <ul class="guidance-tips">
                                    <li>定期盘点库存，核对账实是否相符</li>
                                    <li>检查食材保质期，及时处理临期食材</li>
                                    <li>分析库存周转率，优化采购计划</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤8：留样记录 -->
            <div class="process-step {{ samples.status }}">
                <div class="step-icon">
                    <i class="fas fa-vial"></i>
                </div>
                <div class="step-content">
                    <h5>留样记录</h5>
                    <p class="step-status">{{ samples.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('food_sample.index') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('food_sample.create') }}" class="btn btn-sm btn-success">新建</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceSamples" aria-expanded="false" aria-controls="guidanceSamples">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceSamples">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>进行食品溯源管理</p>
                                <ul class="guidance-tips">
                                    <li>每餐次留样不少于100克</li>
                                    <li>保存时间不少于48小时</li>
                                    <li>记录留样食品名称、留样时间和负责人</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="process-connector">
                <i class="fas fa-chevron-right"></i>
            </div>

            <!-- 步骤9：溯源 -->
            <div class="process-step {{ tracing.status }}">
                <div class="step-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="step-content">
                    <h5>溯源</h5>
                    <p class="step-status">{{ tracing.status_text }}</p>
                    <div class="step-actions">
                        <a href="{{ url_for('food_trace.index') }}" class="btn btn-sm btn-primary">查看</a>
                        <a href="{{ url_for('food_trace.index') }}" class="btn btn-sm btn-info">搜索</a>
                    </div>
                    <div class="step-guidance mt-2">
                        <button class="btn btn-sm btn-link text-info p-0" type="button" data-toggle="collapse"
                                data-target="#guidanceTracing" aria-expanded="false" aria-controls="guidanceTracing">
                            <i class="fas fa-info-circle"></i> 操作指引
                        </button>
                        <div class="collapse" id="guidanceTracing">
                            <div class="guidance-content mt-2">
                                <p class="mb-1"><strong>下一步：</strong>完成食品安全管理闭环</p>
                                <ul class="guidance-tips">
                                    <li>记录食材从采购到消费的全过程</li>
                                    <li>建立食品安全事件应急预案</li>
                                    <li>定期分析溯源数据，优化管理流程</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 今日任务提示 -->
    {% if today_tasks %}
    <div class="today-tasks mt-3">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 今日待办:
            {% for task in today_tasks %}
                {{ task }}{% if not loop.last %}, {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endmacro %}
