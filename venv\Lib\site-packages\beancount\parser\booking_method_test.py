"""Tests for individual booking methods.

Note that these should be already covered by the tests in booking_full_test, but
we may want to add more tests here, just calling each method directly and
covering all the possible branches.
"""

__copyright__ = "Copyright (C) 2017  <PERSON>"
__license__ = "GNU GPLv2"

import unittest

# pylint: disable=unused-import
from beancount.parser import booking_method as bm


if __name__ == '__main__':
    unittest.main()
