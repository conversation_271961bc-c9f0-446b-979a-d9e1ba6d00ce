{% extends "base.html" %}

{% block title %}系统设置帮助{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('help.index') }}">帮助中心</a></li>
            <li class="breadcrumb-item active">系统设置</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h1><i class="fas fa-cogs"></i> 系统设置帮助</h1>
                    <p class="lead">用户管理、权限配置、系统维护功能指南</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-users-cog"></i> 用户管理</h5>
                </div>
                <div class="card-body">
                    <p>用户账号和权限管理。</p>
                    <ul>
                        <li>用户创建和编辑</li>
                        <li>密码重置</li>
                        <li>账号状态管理</li>
                        <li>用户信息维护</li>
                    </ul>
                    <a href="{{ url_for('school_admin.users') }}" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 用户管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-map-marked-alt"></i> 区域管理</h5>
                </div>
                <div class="card-body">
                    <p>学校区域和组织架构。</p>
                    <ul>
                        <li>区域创建和编辑</li>
                        <li>组织架构设置</li>
                        <li>区域权限配置</li>
                        <li>数据隔离管理</li>
                    </ul>
                    <a href="{{ url_for('area.index') }}" class="btn btn-success">
                        <i class="fas fa-external-link-alt"></i> 区域管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-shield-alt"></i> 权限管理</h5>
                </div>
                <div class="card-body">
                    <p>角色和权限配置。</p>
                    <ul>
                        <li>角色定义</li>
                        <li>权限分配</li>
                        <li>权限审核</li>
                        <li>权限继承</li>
                    </ul>
                    <a href="{{ url_for('school_admin.roles') }}" class="btn btn-info">
                        <i class="fas fa-external-link-alt"></i> 权限管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-database"></i> 数据管理</h5>
                </div>
                <div class="card-body">
                    <p>数据备份和系统维护。</p>
                    <ul>
                        <li>数据备份</li>
                        <li>数据恢复</li>
                        <li>系统日志</li>
                        <li>性能监控</li>
                    </ul>
                    <a href="{{ url_for('admin.data_management') }}" class="btn btn-secondary">
                        <i class="fas fa-external-link-alt"></i> 数据管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回帮助中心 -->
    <div class="row">
        <div class="col-12 text-center">
            <a href="{{ url_for('help.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> 返回帮助中心
            </a>
        </div>
    </div>
</div>
{% endblock %}
