Metadata-Version: 2.1
Name: fava
Version: 1.29
Summary: Web interface for the accounting tool Beancount.
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <mail@jakob<PERSON>nitzer.de>
License: MIT License
Project-URL: Homepage, https://beancount.github.io/fava/
Project-URL: Changelog, https://beancount.github.io/fava/changelog.html
Project-URL: Source, https://github.com/beancount/fava/
Project-URL: Issues, https://github.com/beancount/fava/issues/
Project-URL: Chat, https://gitter.im/beancount/fava
Keywords: fava,beancount,accounting
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Flask
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: Intended Audience :: Information Technology
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: JavaScript
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Office/Business :: Financial :: Accounting
Classifier: Topic :: Office/Business :: Financial :: Investment
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: Babel<3,>=2.11
Requires-Dist: Flask-Babel<5,>=1
Requires-Dist: Flask<4,>=2.2
Requires-Dist: Jinja2<4,>=3
Requires-Dist: Werkzeug<4,>=2.2
Requires-Dist: beancount<3,>=2.3.5
Requires-Dist: cheroot<11,>=8
Requires-Dist: click<9,>=7
Requires-Dist: markdown2<3,>=2.3.0
Requires-Dist: ply
Requires-Dist: simplejson<4,>=3.16.0
Requires-Dist: watchfiles>=0.20.0
Provides-Extra: dev
Requires-Dist: build; extra == "dev"
Requires-Dist: furo; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pre-commit; extra == "dev"
Requires-Dist: pyinstaller; extra == "dev"
Requires-Dist: pylint; extra == "dev"
Requires-Dist: pytest; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: setuptools; extra == "dev"
Requires-Dist: sphinx; extra == "dev"
Requires-Dist: sphinx-autodoc-typehints; extra == "dev"
Requires-Dist: tox; extra == "dev"
Requires-Dist: tox-uv; extra == "dev"
Requires-Dist: twine; extra == "dev"
Requires-Dist: types-setuptools; extra == "dev"
Requires-Dist: types-simplejson; extra == "dev"
Provides-Extra: excel
Requires-Dist: pyexcel>=0.5; extra == "excel"
Requires-Dist: pyexcel-ods3>=0.5; extra == "excel"
Requires-Dist: pyexcel-xlsx>=0.5; extra == "excel"
Provides-Extra: old_deps_pins
Requires-Dist: bottle>=0.12.20; extra == "old-deps-pins"
Requires-Dist: importlib-metadata>=3.6; extra == "old-deps-pins"
Requires-Dist: jaraco-functools>=4; extra == "old-deps-pins"
Requires-Dist: lxml>=5.3.0; extra == "old-deps-pins"
Requires-Dist: more-itertools>=6; extra == "old-deps-pins"
Requires-Dist: pytest>=7.2; extra == "old-deps-pins"
Requires-Dist: python-dateutil>=2; extra == "old-deps-pins"
Requires-Dist: pytz>=2020; extra == "old-deps-pins"
Requires-Dist: setuptools>=67; extra == "old-deps-pins"
Requires-Dist: six>=1.16; extra == "old-deps-pins"

.. image:: https://badges.gitter.im/beancount/fava.svg
   :alt: Join the chat at https://gitter.im/beancount/fava
   :target: https://gitter.im/beancount/fava
.. image:: https://img.shields.io/pypi/l/fava.svg
   :target: https://pypi.python.org/pypi/fava
.. image:: https://img.shields.io/pypi/v/fava.svg
   :target: https://pypi.python.org/pypi/fava

Fava is a web interface for the double-entry bookkeeping software `Beancount
<http://furius.ca/beancount/>`__ with a focus on features and usability.

Check out the online `demo <https://fava.pythonanywhere.com>`__ and learn more
about Fava on the `website <https://beancount.github.io/fava/>`__.

The `Getting Started
<https://beancount.github.io/fava/usage.html>`__ guide details the installation
and how to get started with Beancount.  If you are already familiar with
Beancount, you can get started with Fava::

    pip3 install fava
    fava ledger.beancount

and visit the web interface at `http://localhost:5000
<http://localhost:5000>`__.

If you want to hack on Fava or run a development version, see the
`Development <https://beancount.github.io/fava/development.html>`__ page on the
website for details. Contributions are very welcome!

.. image:: https://i.imgbox.com/rfb9I7Zw.png
    :alt: Fava Screenshot
    :width: 100%
    :align: center
    :target: https://fava.pythonanywhere.com
