Metadata-Version: 2.1
Name: beancount
Version: 2.3.6
Summary: Command-line Double-Entry Accounting
Home-page: http://furius.ca/beancount
Download-URL: https://github.com/beancount/beancount
Author: <PERSON>
Author-email: <EMAIL>
License: GNU GPLv2 only
Requires-Python: >=3.7
License-File: COPYING
Requires-Dist: pytest
Requires-Dist: python-dateutil
Requires-Dist: ply
Requires-Dist: bottle
Requires-Dist: lxml
Requires-Dist: beautifulsoup4
Requires-Dist: chardet
Requires-Dist: requests
Requires-Dist: google-api-python-client
Requires-Dist: pdfminer2
Requires-Dist: python-magic >=0.4.12 ; sys_platform != "win32"


      A double-entry accounting system that uses text files as input.

      Beancount defines a simple data format or "language" that lets you define
      financial transaction records in a text file, load them in memory and
      generate and export a variety of reports, such as balance sheets or income
      statements. It also provides a client with an SQL-like query language to
      filter and aggregate financial data, and a web interface which renders
      those reports to HTML. Finally, it provides the scaffolding required to
      automate the conversion of external data into one's input file in
      Beancount syntax.
      
