{% extends "financial/base.html" %}

{% block title %}明细账管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">明细账管理</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-success btn-sm" onclick="batchGenerateLedgers()">
                            <i class="fas fa-magic"></i> 批量生成
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 查询条件 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="subject_id" class="form-label">会计科目</label>
                                <select class="form-select" id="subject_id" name="subject_id" required>
                                    <option value="">请选择科目</option>
                                    {% for subject in subjects %}
                                    <option value="{{ subject.id }}"
                                            {% if subject.id == subject_id %}selected{% endif %}>
                                        {{ subject.code }} - {{ subject.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="year" class="form-label">年份</label>
                                <select class="form-select" id="year" name="year" required>
                                    {% for y in range(2020, 2030) %}
                                    <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}年</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="month" class="form-label">月份</label>
                                <select class="form-select" id="month" name="month" required>
                                    {% for m in range(1, 13) %}
                                    <option value="{{ m }}" {% if m == month %}selected{% endif %}>{{ m }}月</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="btn-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 查看明细账
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="generateLedger()">
                                        <i class="fas fa-magic"></i> 生成明细账
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if generation_status %}
                    <!-- 生成状态提示 -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            {% if generation_status.success %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> {{ generation_status.message }}
                                <br><small>记录数：{{ generation_status.records_count }}，期初余额：{{ "%.2f"|format(generation_status.opening_balance) }}，期末余额：{{ "%.2f"|format(generation_status.closing_balance) }}</small>
                            </div>
                            {% else %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> {{ generation_status.message }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% if selected_subject and ledger_data %}
                    <!-- 科目信息 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5>{{ ledger_data.subject.code }} - {{ ledger_data.subject.name }}</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>科目类型：</strong>{{ ledger_data.subject.subject_type }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>余额方向：</strong>{{ ledger_data.subject.balance_direction }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>期初余额：</strong>{{ "%.2f"|format(ledger_data.opening_balance) }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>期末余额：</strong>{{ "%.2f"|format(ledger_data.closing_balance) }}
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-3">
                                            <strong>本期借方：</strong>{{ "%.2f"|format(ledger_data.total_debit) }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>本期贷方：</strong>{{ "%.2f"|format(ledger_data.total_credit) }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>发生笔数：</strong>{{ ledger_data.transaction_count }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>账页期间：</strong>{{ year }}年{{ month }}月
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 明细账表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped ledger-table">
                            <thead class="table-dark">
                                <tr>
                                    <th width="8%">行号</th>
                                    <th width="12%">日期</th>
                                    <th width="12%">凭证号</th>
                                    <th width="25%">摘要</th>
                                    <th width="12%" class="text-right">借方金额</th>
                                    <th width="12%" class="text-right">贷方金额</th>
                                    <th width="12%" class="text-right">余额</th>
                                    <th width="7%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in ledger_data.records %}
                                <tr {% if record.get('is_opening') %}class="table-warning"{% elif record.get('is_closing') %}class="table-info"{% endif %}>
                                    <td class="text-center">{{ record.line_number }}</td>
                                    <td>
                                        {% if record.voucher_date %}
                                            {{ record.voucher_date.strftime('%Y-%m-%d') if record.voucher_date.strftime else record.voucher_date }}
                                        {% endif %}
                                    </td>
                                    <td>{{ record.voucher_number or '-' }}</td>
                                    <td>
                                        {% if record.get('is_opening') %}
                                            <strong>{{ record.summary }}</strong>
                                        {% elif record.get('is_closing') %}
                                            <strong>{{ record.summary }}</strong>
                                        {% else %}
                                            {{ record.summary }}
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if record.debit_amount > 0 %}
                                            {{ "%.2f"|format(record.debit_amount) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if record.credit_amount > 0 %}
                                            {{ "%.2f"|format(record.credit_amount) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        <strong>{{ "%.2f"|format(record.balance) }}</strong>
                                    </td>
                                    <td class="text-center">
                                        {% if record.get('voucher_id') %}
                                        <a href="{{ url_for('financial.view_voucher', id=record.voucher_id) }}"
                                           class="btn btn-sm btn-outline-primary" title="查看凭证">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle"></i> 请选择会计科目和年月，然后点击"查看明细账"或"生成明细账"
                    </div>
                    {% endif %}

                    {% if not selected_subject %}
                    <!-- 使用说明 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> 明细账功能说明</h6>
                                </div>
                                <div class="card-body">
                                    <h6>什么是明细账？</h6>
                                    <p>明细账是按照会计科目设置的，用来分类登记某一类经济业务，提供有关明细核算资料的账簿。</p>

                                    <h6>明细账的特点：</h6>
                                    <ul>
                                        <li><strong>按月生成</strong>：每个科目按月份生成独立的明细账页</li>
                                        <li><strong>连续记录</strong>：从期初余额开始，按时间顺序记录每笔业务</li>
                                        <li><strong>余额结转</strong>：每笔业务后自动计算并更新余额</li>
                                        <li><strong>标准格式</strong>：符合会计账簿的标准格式要求</li>
                                    </ul>

                                    <h6>使用步骤：</h6>
                                    <ol>
                                        <li>选择要查看的会计科目</li>
                                        <li>选择年份和月份</li>
                                        <li>点击"生成明细账"按钮（首次使用）</li>
                                        <li>点击"查看明细账"查看已生成的明细账</li>
                                        <li>可以批量生成所有有发生额科目的明细账</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导出和打印功能 -->
{% if selected_subject and ledger_data %}
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h6>操作功能</h6>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="exportDetailLedger()">
                        <i class="fas fa-file-excel"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-info" onclick="printDetailLedger()">
                        <i class="fas fa-print"></i> 打印明细账
                    </button>
                    <button type="button" class="btn btn-warning" onclick="regenerateLedger()">
                        <i class="fas fa-redo"></i> 重新生成
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block financial_js %}
<script>
// 生成单个科目明细账
function generateLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId) {
        alert('请先选择科目');
        return;
    }

    if (!year || !month) {
        alert('请选择年份和月份');
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    btn.disabled = true;

    fetch('{{ url_for("financial.generate_detail_ledger_api") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            subject_id: parseInt(subjectId),
            year: parseInt(year),
            month: parseInt(month)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 重新加载页面显示生成的明细账
            window.location.href = `?subject_id=${subjectId}&year=${year}&month=${month}`;
        } else {
            alert('生成失败：' + data.message);
        }
    })
    .catch(error => {
        alert('生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 批量生成明细账
function batchGenerateLedgers() {
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!year || !month) {
        alert('请选择年份和月份');
        return;
    }

    if (!confirm(`确定要批量生成 ${year}年${month}月 所有有发生额科目的明细账吗？`)) {
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 批量生成中...';
    btn.disabled = true;

    fetch('{{ url_for("financial.batch_generate_detail_ledgers") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            year: parseInt(year),
            month: parseInt(month),
            subject_ids: []  // 空数组表示所有有发生额的科目
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 刷新页面
            window.location.reload();
        } else {
            alert('批量生成失败：' + data.message);
        }
    })
    .catch(error => {
        alert('批量生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 重新生成明细账
function regenerateLedger() {
    if (!confirm('确定要重新生成明细账吗？这将覆盖现有的明细账数据。')) {
        return;
    }
    generateLedger();
}

// 导出明细账
function exportDetailLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId || !year || !month) {
        alert('请先选择科目和年月');
        return;
    }

    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const url = `{{ url_for('financial.export_report', report_type='detail_ledger') }}?subject_id=${subjectId}&start_date=${startDate}&end_date=${startDate}`;
    window.open(url, '_blank');
}

// 打印明细账
function printDetailLedger() {
    window.print();
}

// 科目选择变化时的处理
document.getElementById('subject_id').addEventListener('change', function() {
    // 不自动提交，让用户手动选择操作
});

// 添加明细账表格样式
document.addEventListener('DOMContentLoaded', function() {
    // 为明细账表格添加特殊样式
    const ledgerTable = document.querySelector('.ledger-table');
    if (ledgerTable) {
        ledgerTable.style.fontSize = '0.9rem';
        ledgerTable.style.fontFamily = 'monospace';
    }
});
</script>

<style>
.ledger-table {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.ledger-table th {
    background-color: #343a40 !important;
    color: white !important;
    text-align: center;
    vertical-align: middle;
}

.ledger-table td {
    vertical-align: middle;
    padding: 8px;
}

.ledger-table .table-warning {
    background-color: #fff3cd !important;
}

.ledger-table .table-info {
    background-color: #d1ecf1 !important;
}

@media print {
    .card-tools,
    .btn-group,
    .alert {
        display: none !important;
    }

    .ledger-table {
        font-size: 0.8rem;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
