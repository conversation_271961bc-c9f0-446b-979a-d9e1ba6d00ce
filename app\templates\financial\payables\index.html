{% extends "financial/base.html" %}

{% block title %}应付账款管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">应付账款管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.pending_stock_ins') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> 从入库单生成
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" 
                                           value="{{ keyword }}" placeholder="应付账款号或发票号">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="supplier_id">供应商</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id">
                                        <option value="">-- 所有供应商 --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">-- 所有状态 --</option>
                                        <option value="未付款" {% if status == '未付款' %}selected{% endif %}>未付款</option>
                                        <option value="部分付款" {% if status == '部分付款' %}selected{% endif %}>部分付款</option>
                                        <option value="已付清" {% if status == '已付清' %}selected{% endif %}>已付清</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">开始日期</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">结束日期</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        <a href="{{ url_for('financial.payables_index') }}" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-undo"></i> 重置
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 应付账款列表 -->
                    {% if payables.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>应付账款号</th>
                                    <th>供应商</th>
                                    <th>发票号</th>
                                    <th>原始金额</th>
                                    <th>余额</th>
                                    <th>状态</th>
                                    <th>创建日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payable in payables.items %}
                                <tr>
                                    <td>{{ payable.payable_number }}</td>
                                    <td>{{ payable.supplier.name if payable.supplier else '未知供应商' }}</td>
                                    <td>{{ payable.invoice_number or '-' }}</td>
                                    <td class="text-right">{{ "%.2f"|format(payable.original_amount) }}</td>
                                    <td class="text-right">{{ "%.2f"|format(payable.balance_amount) }}</td>
                                    <td>
                                        {% if payable.status == '未付款' %}
                                            <span class="badge badge-danger">{{ payable.status }}</span>
                                        {% elif payable.status == '部分付款' %}
                                            <span class="badge badge-warning">{{ payable.status }}</span>
                                        {% elif payable.status == '已付清' %}
                                            <span class="badge badge-success">{{ payable.status }}</span>
                                        {% else %}
                                            <span class="badge badge-light">{{ payable.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ payable.created_at.strftime('%Y-%m-%d') if payable.created_at else '未知' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if payable.balance_amount > 0 %}
                                            <a href="{{ url_for('financial.create_payment') }}?payable_id={{ payable.id }}" 
                                               class="btn btn-success btn-sm" title="付款">
                                                <i class="fas fa-money-bill"></i>
                                            </a>
                                            {% endif %}
                                            <button class="btn btn-info btn-sm" title="查看详情" 
                                                    onclick="viewPayable({{ payable.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="font-weight-bold">
                                    <td colspan="3" class="text-right">合计：</td>
                                    <td class="text-right">{{ "%.2f"|format(payables.items|sum(attribute='original_amount')) }}</td>
                                    <td class="text-right">{{ "%.2f"|format(payables.items|sum(attribute='balance_amount')) }}</td>
                                    <td colspan="3"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if payables.pages > 1 %}
                    <nav aria-label="应付账款分页">
                        <ul class="pagination justify-content-center">
                            {% if payables.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.payables_index', page=payables.prev_num, keyword=keyword, supplier_id=supplier_id, status=status, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in payables.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != payables.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.payables_index', page=page_num, keyword=keyword, supplier_id=supplier_id, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if payables.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.payables_index', page=payables.next_num, keyword=keyword, supplier_id=supplier_id, status=status, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无应付账款数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewPayable(payableId) {
    alert('查看应付账款详情功能待实现，ID: ' + payableId);
}
</script>
{% endblock %}
