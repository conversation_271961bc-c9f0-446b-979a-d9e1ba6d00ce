{% extends "_layout.html" %}

{% set page_title = _('Options') %}

{% block content %}
<h2>{{ _('Fava options') }} <a href="{{ url_for('help_page', page_slug='options') }}">({{ _('help') }})</a></h2>
<table is="sortable-table" class="options">
  <thead>
    <tr>
      <th data-sort="string" data-order="asc">{{ _('Key') }}</th>
      <th>{{ _('Value') }}</th>
    </tr>
  </thead>
  <tbody>
    {% for field in ledger.fava_options|dataclass_fields %}
    <tr>
      <td>{{ field.name|replace('_', '-') }}</td>
      <td><pre>{{ ledger.fava_options|attr(field.name)|pprint }}</pre></td>
    </tr>
    {% endfor %}
  </tbody>
</table>
<h2>{{ _('Beancount options') }}</h2>
<table is="sortable-table" class="options">
  <thead>
    <tr>
      <th data-sort="string" data-order="asc">{{ _('Key') }}</th>
      <th>{{ _('Value') }}</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in ledger.options|dictsort %}
    <tr>
      <td>{{ key }}</td>
      <td><pre>{{ value }}</pre></td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endblock %}
