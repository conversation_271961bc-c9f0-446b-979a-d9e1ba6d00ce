"""
食材溯源工具模块
提供从菜单到供应商的完整溯源链路
"""

from flask import current_app
from app.models import (
    WeeklyMenu, WeeklyMenuRecipe, Recipe, RecipeIngredient,
    ConsumptionPlan, ConsumptionDetail, StockOut, StockOutItem, Inventory,
    StockIn, StockInItem, PurchaseOrder, PurchaseOrderItem, Supplier, Ingredient,
    FoodSample
)
from datetime import datetime, date, timedelta
from sqlalchemy import and_, or_

def trace_food_supply_chain(date_str, meal_type, area_id=None):
    """
    食材溯源接口函数（优化：先读出当餐次的食谱，再通过食谱所含食材来进行比对）

    参数:
        date_str (str): 日期，格式为'YYYY-MM-DD'
        meal_type (str): 餐次，如'早餐'/'午餐'/'晚餐'
        area_id (int, optional): 区域ID，如果不提供则使用当前用户的区域

    返回:
        dict: 包含完整溯源链的字典
    """
    try:
        trace_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        breakpoints = []

        # 1. 先获取当餐次的食谱信息
        menu_info = get_menu_info(trace_date, meal_type, area_id)
        if not menu_info or not menu_info.get('recipes'):
            breakpoints.append('菜单食谱')
            return {
                'success': True,
                'data': {
                    'menu': menu_info,
                    'recipe_ingredients': [],
                    'consumption_plans': [],
                    'stock_outs': [],
                    'inventory_batches': [],
                    'stock_ins': [],
                    'purchase_orders': [],
                    'suppliers': [],
                    'food_samples': [],
                    'breakpoints': breakpoints
                }
            }

        # 2. 获取食谱所含的食材信息
        recipe_ingredients = get_recipe_ingredients(menu_info['recipes'])
        if not recipe_ingredients:
            breakpoints.append('食谱食材')

        # 3. 基于食材获取消耗计划信息
        consumption_plans = get_consumption_plans_by_ingredients(trace_date, meal_type, area_id, recipe_ingredients)
        if not consumption_plans:
            breakpoints.append('消耗计划')

        # 4. 获取出库记录
        stock_outs = get_stock_outs_by_consumption(consumption_plans)
        if not stock_outs:
            breakpoints.append('出库单')

        # 5. 获取库存批次信息
        inventory_batches = get_inventory_batches(stock_outs)
        if not inventory_batches:
            breakpoints.append('库存批次')

        # 6. 获取入库记录
        stock_ins = get_stock_ins_by_batches(inventory_batches)
        if not stock_ins:
            breakpoints.append('入库单')

        # 7. 获取采购订单信息
        purchase_orders = get_purchase_orders(stock_ins)
        if not purchase_orders:
            breakpoints.append('采购订单')

        # 8. 获取供应商信息
        suppliers = get_suppliers(purchase_orders)
        if not suppliers:
            breakpoints.append('供应商')

        # 9. 获取留样记录
        food_samples = get_food_samples(trace_date, meal_type, area_id)

        # 10. 构建完整的溯源链
        supply_chain = {
            'menu': menu_info,
            'recipe_ingredients': recipe_ingredients,
            'consumption_plans': consumption_plans,
            'stock_outs': stock_outs,
            'inventory_batches': inventory_batches,
            'stock_ins': stock_ins,
            'purchase_orders': purchase_orders,
            'suppliers': suppliers,
            'food_samples': food_samples,
            'breakpoints': breakpoints
        }

        return {
            'success': True,
            'data': supply_chain
        }

    except Exception as e:
        current_app.logger.error(f"食材溯源失败: {str(e)}")
        return {
            'success': False,
            'message': f'溯源失败: {str(e)}'
        }

def get_menu_info(trace_date, meal_type, area_id=None):
    """获取菜单信息"""
    # 查找周菜单
    weekday = trace_date.weekday()  # 0-6，0表示周一
    week_start = trace_date - timedelta(days=weekday)
    week_end = week_start + timedelta(days=6)

    weekly_menu_query = WeeklyMenu.query.filter(
        WeeklyMenu.week_start <= trace_date,
        WeeklyMenu.week_end >= trace_date,
        WeeklyMenu.status == '已发布'
    )

    if area_id:
        weekly_menu_query = weekly_menu_query.filter(WeeklyMenu.area_id == area_id)

    weekly_menu = weekly_menu_query.first()

    # 日菜单功能已移除，只使用周菜单

    # 整合菜单信息
    menu_info = {
        'date': trace_date.strftime('%Y-%m-%d'),
        'meal_type': meal_type,
        'area_id': area_id,
        'weekly_menu': weekly_menu.to_dict() if weekly_menu else None,
        'recipes': []
    }

    # 获取菜品信息
    if weekly_menu:
        weekly_recipes = WeeklyMenuRecipe.query.filter(
            WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
            WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
            WeeklyMenuRecipe.meal_type == meal_type
        ).all()

        for weekly_recipe in weekly_recipes:
            if weekly_recipe.recipe:
                # 只获取基本信息，不包含图片
                recipe_info = {
                    'id': weekly_recipe.recipe.id,
                    'name': weekly_recipe.recipe.name,
                    'category': weekly_recipe.recipe.category,
                    'notes': weekly_recipe.recipe.notes,
                    'source': 'weekly_menu'
                }
                menu_info['recipes'].append(recipe_info)

    # 日菜单功能已移除，不再处理 menu_plan 数据

    return menu_info

def get_recipe_ingredients(recipes):
    """获取食谱所含的食材信息"""
    from app.models import RecipeIngredient, Ingredient

    if not recipes:
        return []

    recipe_ids = [recipe['id'] for recipe in recipes]

    # 查询食谱食材关联
    recipe_ingredients = RecipeIngredient.query.filter(
        RecipeIngredient.recipe_id.in_(recipe_ids)
    ).all()

    result = []
    for ri in recipe_ingredients:
        if ri.ingredient:
            ingredient_info = {
                'recipe_id': ri.recipe_id,
                'recipe_name': next((r['name'] for r in recipes if r['id'] == ri.recipe_id), ''),
                'ingredient_id': ri.ingredient_id,
                'ingredient_name': ri.ingredient.name,
                'ingredient_category': ri.ingredient.category.name if ri.ingredient.category else None,
                'quantity': float(ri.quantity) if ri.quantity else 0,
                'unit': ri.unit,
                'notes': ri.notes
            }
            result.append(ingredient_info)

    return result

def get_consumption_plans_by_ingredients(trace_date, meal_type, area_id, recipe_ingredients):
    """基于食材获取消耗计划信息"""
    from app.models import ConsumptionPlan, ConsumptionDetail

    if not recipe_ingredients:
        return []

    # 获取食材ID列表
    ingredient_ids = list(set([ri['ingredient_id'] for ri in recipe_ingredients]))

    # 查找消耗计划 - 现在直接通过 area_id 查询
    query = ConsumptionPlan.query.filter(
        ConsumptionPlan.consumption_date == trace_date,
        ConsumptionPlan.meal_type == meal_type
    )

    if area_id:
        # 直接通过消耗计划的 area_id 过滤
        query = query.filter(ConsumptionPlan.area_id == area_id)

    consumption_plans = query.all()

    result = []
    for plan in consumption_plans:
        # 获取消耗明细，只包含相关食材
        details = ConsumptionDetail.query.filter(
            ConsumptionDetail.consumption_plan_id == plan.id,
            ConsumptionDetail.ingredient_id.in_(ingredient_ids)
        ).all()

        if details:  # 只有包含相关食材的消耗计划才加入结果
            plan_data = {
                'id': plan.id,
                'consumption_date': plan.consumption_date.strftime('%Y-%m-%d') if plan.consumption_date else None,
                'meal_type': plan.meal_type,
                'diners_count': plan.diners_count,

                'status': plan.status,
                'notes': plan.notes,
                'details': []
            }

            for detail in details:
                detail_data = {
                    'id': detail.id,
                    'ingredient_id': detail.ingredient_id,
                    'ingredient_name': detail.ingredient.name if detail.ingredient else None,
                    'planned_quantity': float(detail.planned_quantity) if detail.planned_quantity else 0,
                    'actual_quantity': float(detail.actual_quantity) if detail.actual_quantity else 0,
                    'unit': detail.unit,
                    'notes': detail.notes
                }
                plan_data['details'].append(detail_data)

            result.append(plan_data)

    return result

def get_consumption_plans(trace_date, meal_type, area_id=None):
    """获取消耗计划信息"""
    query = ConsumptionPlan.query.filter(
        ConsumptionPlan.consumption_date == trace_date,
        ConsumptionPlan.meal_type == meal_type
    )

    if area_id:
        # 直接通过消耗计划的 area_id 过滤
        query = query.filter(ConsumptionPlan.area_id == area_id)

    consumption_plans = query.all()

    result = []
    for plan in consumption_plans:
        plan_data = plan.to_dict()
        # 获取消耗明细
        details = ConsumptionDetail.query.filter_by(consumption_plan_id=plan.id).all()
        plan_data['details'] = [detail.to_dict() for detail in details]
        result.append(plan_data)

    return result

def get_stock_outs_by_consumption(consumption_plans):
    """根据消耗计划获取出库记录"""
    if not consumption_plans:
        return []

    plan_ids = [plan['id'] for plan in consumption_plans]
    stock_outs = StockOut.query.filter(StockOut.consumption_plan_id.in_(plan_ids)).all()

    result = []
    for stock_out in stock_outs:
        stock_out_data = {
            'id': stock_out.id,
            'stock_out_number': stock_out.stock_out_number,
            'warehouse_id': stock_out.warehouse_id,
            'warehouse_name': stock_out.warehouse.name if stock_out.warehouse else None,
            'consumption_plan_id': stock_out.consumption_plan_id,
            'stock_out_date': stock_out.stock_out_date.strftime('%Y-%m-%d') if stock_out.stock_out_date else None,
            'operator_id': stock_out.operator_id,
            'operator_name': stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else None,
            'status': stock_out.status,
            'notes': stock_out.notes,
            'items': []
        }

        # 获取出库明细
        items = StockOutItem.query.filter_by(stock_out_id=stock_out.id).all()
        for item in items:
            # 直接从出库明细中获取批次号，而不是通过库存记录
            batch_number = item.batch_number

            # 如果出库明细中没有批次号，则尝试从库存记录中获取
            if not batch_number and item.inventory:
                batch_number = item.inventory.batch_number

            item_data = {
                'id': item.id,
                'inventory_id': item.inventory_id,
                'ingredient_id': item.ingredient_id,
                'ingredient_name': item.ingredient.name if item.ingredient else None,
                'quantity': float(item.quantity) if item.quantity else 0,
                'unit': item.unit,
                'batch_number': batch_number,
                'expiry_date': item.inventory.expiry_date.strftime('%Y-%m-%d') if item.inventory and item.inventory.expiry_date else None
            }
            stock_out_data['items'].append(item_data)

        result.append(stock_out_data)

    # 记录日志
    current_app.logger.info(f"查询到 {len(result)} 条出库记录")
    for stock_out in result:
        current_app.logger.info(f"出库单 {stock_out['stock_out_number']} 包含 {len(stock_out['items'])} 条明细")
        for item in stock_out['items']:
            current_app.logger.info(f"  - 食材: {item['ingredient_name']}, 批次号: {item['batch_number']}")

    return result

def get_inventory_batches(stock_outs):
    """根据出库记录获取库存批次信息"""
    if not stock_outs:
        return []

    # 收集批次号和库存ID
    batch_numbers = []
    inventory_ids = []

    for stock_out in stock_outs:
        for item in stock_out['items']:
            if item['batch_number']:
                batch_numbers.append(item['batch_number'])
            if item['inventory_id']:
                inventory_ids.append(item['inventory_id'])

    # 去重
    batch_numbers = list(set(batch_numbers))
    inventory_ids = list(set(inventory_ids))

    if not batch_numbers and not inventory_ids:
        return []

    # 查询库存记录
    query = Inventory.query

    if batch_numbers and inventory_ids:
        # 同时使用批次号和库存ID查询
        query = query.filter(or_(
            Inventory.batch_number.in_(batch_numbers),
            Inventory.id.in_(inventory_ids)
        ))
    elif batch_numbers:
        # 只使用批次号查询
        query = query.filter(Inventory.batch_number.in_(batch_numbers))
    else:
        # 只使用库存ID查询
        query = query.filter(Inventory.id.in_(inventory_ids))

    inventories = query.all()

    # 记录日志
    current_app.logger.info(f"查询到 {len(inventories)} 条库存记录，批次号: {batch_numbers}, 库存ID: {inventory_ids}")

    result = []
    for inventory in inventories:
        inventory_data = {
            'id': inventory.id,
            'batch_number': inventory.batch_number,
            'ingredient_id': inventory.ingredient_id,
            'ingredient_name': inventory.ingredient.name if inventory.ingredient else None,
            'warehouse_id': inventory.warehouse_id,
            'warehouse_name': inventory.warehouse.name if inventory.warehouse else None,
            'storage_location_id': inventory.storage_location_id,
            'storage_location_name': inventory.storage_location.name if inventory.storage_location else None,
            'quantity': float(inventory.quantity) if inventory.quantity else 0,
            'unit': inventory.unit,
            'production_date': inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else None,
            'expiry_date': inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else None,
            'stock_in_id': inventory.stock_in_id
        }
        result.append(inventory_data)

    return result

def get_stock_ins_by_batches(inventory_batches):
    """根据库存批次获取入库记录"""
    if not inventory_batches:
        return []

    # 收集批次号和入库单ID
    batch_numbers = []
    stock_in_ids = []

    for batch in inventory_batches:
        if batch['batch_number']:
            batch_numbers.append(batch['batch_number'])
        if batch['stock_in_id']:
            stock_in_ids.append(batch['stock_in_id'])

    # 去重
    batch_numbers = list(set(batch_numbers))
    stock_in_ids = list(set(stock_in_ids))

    if not batch_numbers and not stock_in_ids:
        return []

    # 首先通过批次号查询入库明细
    stock_in_items = []
    if batch_numbers:
        stock_in_items = StockInItem.query.filter(StockInItem.batch_number.in_(batch_numbers)).all()
        # 从入库明细中获取入库单ID
        for item in stock_in_items:
            if item.stock_in_id and item.stock_in_id not in stock_in_ids:
                stock_in_ids.append(item.stock_in_id)

    # 记录日志
    current_app.logger.info(f"通过批次号 {batch_numbers} 查询到 {len(stock_in_items)} 条入库明细")
    current_app.logger.info(f"查询入库单ID: {stock_in_ids}")

    if not stock_in_ids:
        return []

    # 查询入库单
    stock_ins = StockIn.query.filter(StockIn.id.in_(stock_in_ids)).all()

    result = []
    for stock_in in stock_ins:
        stock_in_data = {
            'id': stock_in.id,
            'stock_in_number': stock_in.stock_in_number,
            'warehouse_id': stock_in.warehouse_id,
            'warehouse_name': stock_in.warehouse.name if stock_in.warehouse else None,
            'supplier_id': stock_in.supplier_id,
            'supplier_name': stock_in.supplier.name if stock_in.supplier else None,
            'purchase_order_id': stock_in.purchase_order_id,
            'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else None,
            'stock_in_type': stock_in.stock_in_type,
            'operator_id': stock_in.operator_id,
            'operator_name': stock_in.operator.real_name or stock_in.operator.username if stock_in.operator else None,
            'status': stock_in.status,
            'notes': stock_in.notes,
            'items': []
        }

        # 获取入库明细
        items = StockInItem.query.filter_by(stock_in_id=stock_in.id).all()
        for item in items:
            item_data = {
                'id': item.id,
                'ingredient_id': item.ingredient_id,
                'ingredient_name': item.ingredient.name if item.ingredient else None,
                'quantity': float(item.quantity) if item.quantity else 0,
                'unit': item.unit,
                'batch_number': item.batch_number,
                'production_date': item.production_date.strftime('%Y-%m-%d') if item.production_date else None,
                'expiry_date': item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else None,
                'unit_price': float(item.unit_price) if item.unit_price else 0,
                'total_price': float(item.total_price) if item.total_price else 0
            }
            stock_in_data['items'].append(item_data)

        result.append(stock_in_data)

    return result

def get_purchase_orders(stock_ins):
    """根据入库记录获取采购订单信息"""
    if not stock_ins:
        return []

    purchase_order_ids = []
    for stock_in in stock_ins:
        if stock_in['purchase_order_id']:
            purchase_order_ids.append(stock_in['purchase_order_id'])

    if not purchase_order_ids:
        return []

    purchase_orders = PurchaseOrder.query.filter(PurchaseOrder.id.in_(purchase_order_ids)).all()

    result = []
    for order in purchase_orders:
        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else None,
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else None,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'order_date': order.order_date.strftime('%Y-%m-%d %H:%M:%S') if order.order_date else None,
            'expected_delivery_date': order.expected_delivery_date.strftime('%Y-%m-%d') if order.expected_delivery_date else None,
            'status': order.status,
            'created_by': order.created_by,
            'creator_name': order.creator.real_name or order.creator.username if order.creator else None,
            'items': []
        }

        # 获取采购订单明细
        items = PurchaseOrderItem.query.filter_by(order_id=order.id).all()
        for item in items:
            item_data = {
                'id': item.id,
                'ingredient_id': item.ingredient_id,
                'ingredient_name': item.ingredient.name if item.ingredient else None,
                'quantity': float(item.quantity) if item.quantity else 0,
                'unit': item.unit,
                'unit_price': float(item.unit_price) if item.unit_price else 0,
                'total_price': float(item.total_price) if item.total_price else 0
            }
            order_data['items'].append(item_data)

        result.append(order_data)

    return result

def get_suppliers(purchase_orders):
    """根据采购订单获取供应商信息"""
    if not purchase_orders:
        return []

    supplier_ids = []
    for order in purchase_orders:
        if order['supplier_id']:
            supplier_ids.append(order['supplier_id'])

    if not supplier_ids:
        return []

    suppliers = Supplier.query.filter(Supplier.id.in_(supplier_ids)).all()

    result = []
    for supplier in suppliers:
        supplier_data = {
            'id': supplier.id,
            'name': supplier.name,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'business_license': supplier.business_license,
            'status': supplier.status,
            'category_id': supplier.category_id,
            'category_name': supplier.category.name if supplier.category else None
        }
        result.append(supplier_data)

    return result

def get_food_samples(trace_date, meal_type, area_id=None):
    """获取留样记录"""
    query = FoodSample.query.filter(
        FoodSample.meal_date == trace_date,
        FoodSample.meal_type == meal_type
    )

    if area_id:
        query = query.filter(FoodSample.area_id == area_id)

    food_samples = query.all()

    result = []
    for sample in food_samples:
        sample_data = {
            'id': sample.id,
            'sample_number': sample.sample_number,
            'recipe_id': sample.recipe_id,
            'recipe_name': sample.recipe.name if sample.recipe else None,
            'area_id': sample.area_id,
            'area_name': sample.area.name if sample.area else None,

            'meal_date': sample.meal_date.strftime('%Y-%m-%d') if sample.meal_date else None,
            'meal_type': sample.meal_type,
            'sample_image': sample.sample_image,
            'sample_quantity': float(sample.sample_quantity) if sample.sample_quantity else 0,
            'sample_unit': sample.sample_unit,
            'storage_location': sample.storage_location,
            'storage_temperature': sample.storage_temperature,
            'start_time': sample.start_time.strftime('%Y-%m-%d %H:%M:%S') if sample.start_time else None,
            'end_time': sample.end_time.strftime('%Y-%m-%d %H:%M:%S') if sample.end_time else None,
            'status': sample.status
        }
        result.append(sample_data)

    return result
