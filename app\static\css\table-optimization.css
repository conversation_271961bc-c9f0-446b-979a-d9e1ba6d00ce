/* 表格优化样式 - 专注于排版和用户体验 */

/* === 基础表格优化 === */

/* 表格容器 */
.table-responsive {
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

/* 表格基础样式 */
.table {
  margin-bottom: 0;
  font-size: 1.125rem; /* 18px */
  line-height: 1.4;
}

/* === 表头优化 === */

/* 表头样式 - 整体渐变背景 */
.table thead {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.table thead th {
  background: transparent;
  color: white;
  border: none;
  font-weight: normal !important;
  font-size: 0.875rem !important; /* 14px */
  padding: 16px 10px; /* 增加内边距，让表头更高 */
  text-align: center;
  vertical-align: middle;
  white-space: nowrap; /* 防止表头文字换行 */
  position: sticky;
  top: 0;
  z-index: 10;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: none; /* 移除过渡效果 */
}

/* 最后一列表头不需要右边框 */
.table thead th:last-child {
  border-right: none;
}

/* 表头文字优化 */
.table thead th small {
  display: block;
  font-size: 0.85rem;
  opacity: 0.9;
  margin-top: 2px;
}

/* 表头排序图标 */
.table thead th .sort-icon {
  margin-left: 4px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.table thead th:hover .sort-icon {
  opacity: 1;
}

/* === 表格内容优化 === */

/* 表格行样式 */
.table tbody tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--theme-gray-200, #e5e7eb);
}

.table tbody tr:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.03);
  transform: scale(1.001);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 表格单元格 */
.table tbody td {
  padding: 12px 10px;
  vertical-align: middle;
  border-top: none;
  font-size: 1.125rem; /* 18px */
  line-height: 1.3;
}

/* 文字内容优化 */
.table tbody td.text-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table tbody td.text-content:hover {
  white-space: normal;
  overflow: visible;
  position: relative;
  z-index: 5;
  background-color: var(--theme-surface, #ffffff);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 12px;
}

/* === 操作按钮优化 === */

/* 操作列样式 */
.table tbody td.action-column {
  width: 120px;
  text-align: center;
  white-space: nowrap;
  padding: 8px 4px;
}

/* 按钮组优化 */
.btn-group-sm .btn {
  padding: 4px 8px;
  font-size: 0.75rem;
  margin: 0 1px;
  border-radius: 4px;
}

.btn-group-sm .btn i {
  font-size: 0.8rem;
}

/* === 状态标签优化 === */

/* 状态徽章 */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
  white-space: nowrap;
}

.status-badge.status-success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.status-badge.status-warning {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.status-badge.status-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.status-badge.status-info {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

/* === 数字列优化 === */

/* 数字对齐 */
.table tbody td.number-column {
  text-align: right;
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* 金额显示 */
.amount-display {
  color: var(--theme-primary);
  font-weight: 600;
}

.amount-display.negative {
  color: var(--theme-danger);
}

.amount-display.positive {
  color: var(--theme-success);
}

/* === 日期时间优化 === */

/* 日期时间列 */
.table tbody td.datetime-column {
  font-size: 0.9rem;
  color: var(--theme-gray-600, #6b7280);
  white-space: nowrap;
}

.datetime-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.datetime-display .date {
  font-weight: 500;
  color: var(--theme-gray-800, #1f2937);
}

.datetime-display .time {
  font-size: 0.8rem;
  color: var(--theme-gray-500, #6b7280);
}

/* === 响应式优化 === */

/* 移动端优化 */
@media (max-width: 768px) {
  .table {
    font-size: 0.9rem;
  }

  .table thead th {
    padding: 12px 6px;
    font-size: 0.8rem !important;
    font-weight: normal !important;
    transition: none; /* 移除过渡效果 */
  }

  .table tbody td {
    padding: 10px 6px;
    font-size: 0.9rem;
  }

  .table tbody td.action-column {
    width: 80px;
  }

  .btn-group-sm .btn {
    padding: 2px 4px;
    font-size: 0.7rem;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
    min-width: 50px;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .table {
    font-size: 0.95rem;
  }

  .table thead th {
    padding: 14px 8px;
    font-size: 0.875rem !important;
    font-weight: normal !important;
  }

  .table tbody td {
    padding: 11px 8px;
    font-size: 0.95rem;
  }
}

/* === 特殊表格类型 === */

/* 紧凑型表格 */
.table-compact {
  font-size: 1.125rem; /* 18px */
}

.table-compact thead {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.table-compact thead th {
  background: transparent;
  padding: 12px 8px;
  font-size: 0.875rem !important; /* 14px */
  font-weight: normal !important;
  transition: none; /* 移除过渡效果 */
}

.table-compact tbody td {
  padding: 10px 8px;
  font-size: 1.125rem; /* 18px */
}

/* 宽松型表格 */
.table-spacious {
  font-size: 1.25rem; /* 20px */
}

.table-spacious thead {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.table-spacious thead th {
  background: transparent;
  padding: 20px 14px;
  font-size: 0.875rem !important; /* 14px */
  font-weight: normal !important;
}

.table-spacious tbody td {
  padding: 16px 14px;
  font-size: 1.25rem; /* 20px */
}

/* === 表格工具栏 === */

/* 表格上方工具栏 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background-color: var(--theme-surface, #ffffff);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-toolbar .toolbar-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 搜索框优化 */
.table-search {
  position: relative;
  max-width: 300px;
}

.table-search input {
  padding-left: 2.5rem;
  border-radius: 20px;
  border: 1px solid var(--theme-gray-300, #d1d5db);
}

.table-search .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-gray-400, #9ca3af);
}

/* === 加载状态 === */

/* 表格加载状态 */
.table-loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

/* 空状态 */
.table-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--theme-gray-500, #6b7280);
}

.table-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.table-empty h5 {
  margin-bottom: 0.5rem;
  color: var(--theme-gray-700, #374151);
}

.table-empty p {
  margin-bottom: 0;
  font-size: 0.9rem;
}
