/**
 * 专业记账凭证编辑器
 * 类似Excel的表格编辑体验
 */
class ProfessionalVoucherEditor {
    constructor(voucherId, options = {}) {
        this.voucherId = voucherId;
        this.options = {
            autoSave: true,
            enableKeyboardNavigation: true,
            enableAutocomplete: true,
            ...options
        };
        
        this.currentCell = null;
        this.currentRow = 0;
        this.currentCol = 0;
        this.data = [];
        this.subjects = [];
        this.isEditing = false;
        this.clipboard = null;
        
        this.init();
    }
    
    init() {
        this.loadSubjects();
        this.loadVoucherData();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.createToolbar();
    }
    
    loadSubjects() {
        fetch('/financial/accounting-subjects/api?include_system=true')
            .then(response => response.json())
            .then(data => {
                this.subjects = data;
                this.setupSubjectAutocomplete();
            })
            .catch(error => {
                console.error('加载科目失败:', error);
                this.showMessage('加载科目失败', 'error');
            });
    }
    
    loadVoucherData() {
        fetch(`/financial/vouchers/${this.voucherId}/details`)
            .then(response => response.json())
            .then(data => {
                this.data = data.details || [];
                this.renderTable();
                this.updateTotals();
            })
            .catch(error => {
                console.error('加载凭证数据失败:', error);
                this.showMessage('加载凭证数据失败', 'error');
            });
    }
    
    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'voucher-toolbar';
        toolbar.innerHTML = `
            <button class="btn btn-success btn-sm" onclick="voucherEditor.addRow()">
                <i class="fas fa-plus"></i> 添加行 (Ctrl+N)
            </button>
            <button class="btn btn-danger btn-sm" onclick="voucherEditor.deleteRow()">
                <i class="fas fa-trash"></i> 删除行 (Del)
            </button>
            <button class="btn btn-info btn-sm" onclick="voucherEditor.copyRow()">
                <i class="fas fa-copy"></i> 复制行 (Ctrl+C)
            </button>
            <button class="btn btn-warning btn-sm" onclick="voucherEditor.pasteRow()">
                <i class="fas fa-paste"></i> 粘贴行 (Ctrl+V)
            </button>
            <button class="btn btn-primary btn-sm" onclick="voucherEditor.saveVoucher()">
                <i class="fas fa-save"></i> 保存 (Ctrl+S)
            </button>
            <button class="btn btn-secondary btn-sm" onclick="voucherEditor.checkBalance()">
                <i class="fas fa-balance-scale"></i> 检查平衡 (F9)
            </button>
            <span class="balance-indicator" id="balance-indicator">未检查</span>
        `;
        
        const container = document.querySelector('.voucher-editor-container');
        if (container) {
            container.insertBefore(toolbar, container.firstChild);
        }
    }
    
    renderTable() {
        const container = document.querySelector('.voucher-table-section');
        if (!container) return;
        
        let html = `
            <table class="voucher-editor-table" id="voucher-table">
                <thead>
                    <tr>
                        <th width="5%">行号</th>
                        <th width="25%">摘要</th>
                        <th width="30%">会计科目</th>
                        <th width="18%">
                            <div>借方金额</div>
                            <div class="amount-units">
                                <span>千</span><span>百</span><span>十</span><span>万</span>
                                <span>千</span><span>百</span><span>十</span><span>元</span>
                                <span>角</span><span>分</span>
                            </div>
                        </th>
                        <th width="18%">
                            <div>贷方金额</div>
                            <div class="amount-units">
                                <span>千</span><span>百</span><span>十</span><span>万</span>
                                <span>千</span><span>百</span><span>十</span><span>元</span>
                                <span>角</span><span>分</span>
                            </div>
                        </th>
                        <th width="4%">操作</th>
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
        `;
        
        // 渲染数据行
        this.data.forEach((row, index) => {
            html += this.renderRow(row, index);
        });
        
        // 添加空行用于新增
        if (this.data.length === 0) {
            html += this.renderRow({}, 0);
        }
        
        html += `
                </tbody>
                <tfoot>
                    <tr class="total-row">
                        <td colspan="3"><strong>合计：</strong></td>
                        <td class="amount-cell"><strong id="debit-total">0.00</strong></td>
                        <td class="amount-cell"><strong id="credit-total">0.00</strong></td>
                        <td></td>
                    </tr>
                    <tr class="amount-words-row">
                        <td colspan="6" id="amount-in-words">
                            <strong>合计大写：</strong><span id="amount-words-text">零元整</span>
                        </td>
                    </tr>
                </tfoot>
            </table>
        `;
        
        container.innerHTML = html;
        this.setupTableEvents();
    }
    
    renderRow(rowData, index) {
        const rowNumber = index + 1;
        const debitAmount = this.formatAmount(rowData.debit_amount || 0);
        const creditAmount = this.formatAmount(rowData.credit_amount || 0);
        const subjectDisplay = rowData.subject ? `${rowData.subject.code} - ${rowData.subject.name}` : '';
        
        return `
            <tr data-row="${index}" data-detail-id="${rowData.id || ''}">
                <td class="row-number">${rowNumber}</td>
                <td>
                    <textarea class="editable-cell summary-cell" 
                              data-col="summary" 
                              placeholder="输入摘要..."
                              rows="1">${rowData.summary || ''}</textarea>
                </td>
                <td>
                    <div class="subject-selector">
                        <input type="text" 
                               class="editable-cell subject-cell" 
                               data-col="subject"
                               placeholder="选择会计科目..."
                               value="${subjectDisplay}"
                               readonly>
                        <input type="hidden" class="subject-id" value="${rowData.subject_id || ''}">
                        <div class="subject-dropdown"></div>
                    </div>
                </td>
                <td class="amount-cell">
                    <input type="text" 
                           class="editable-cell amount-input debit" 
                           data-col="debit"
                           placeholder="0.00"
                           value="${debitAmount}">
                </td>
                <td class="amount-cell">
                    <input type="text" 
                           class="editable-cell amount-input credit" 
                           data-col="credit"
                           placeholder="0.00"
                           value="${creditAmount}">
                </td>
                <td class="row-actions">
                    <button class="btn btn-sm btn-outline-danger" onclick="voucherEditor.deleteRowByIndex(${index})" title="删除行">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            </tr>
        `;
    }
    
    setupTableEvents() {
        const table = document.getElementById('voucher-table');
        if (!table) return;
        
        // 单元格点击事件
        table.addEventListener('click', (e) => {
            const cell = e.target.closest('.editable-cell');
            if (cell) {
                this.selectCell(cell);
            }
        });
        
        // 单元格输入事件
        table.addEventListener('input', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.handleCellInput(e.target);
            }
        });
        
        // 科目选择事件
        table.addEventListener('click', (e) => {
            if (e.target.classList.contains('subject-cell')) {
                this.openSubjectSelector(e.target);
            }
        });
        
        // 失去焦点时保存
        table.addEventListener('blur', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.saveCellData(e.target);
            }
        }, true);
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveVoucher();
                return;
            }
            
            // Ctrl+N 新增行
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.addRow();
                return;
            }
            
            // F9 检查平衡
            if (e.key === 'F9') {
                e.preventDefault();
                this.checkBalance();
                return;
            }
            
            // 如果当前有选中的单元格
            if (this.currentCell) {
                this.handleKeyboardNavigation(e);
            }
        });
    }
    
    handleKeyboardNavigation(e) {
        const table = document.getElementById('voucher-table');
        const rows = table.querySelectorAll('tbody tr');
        const currentRow = rows[this.currentRow];
        
        switch (e.key) {
            case 'Tab':
                e.preventDefault();
                if (e.shiftKey) {
                    this.moveToPreviousCell();
                } else {
                    this.moveToNextCell();
                }
                break;
                
            case 'Enter':
                e.preventDefault();
                this.moveToNextRow();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.moveToPreviousRow();
                break;
                
            case 'ArrowDown':
                e.preventDefault();
                this.moveToNextRow();
                break;
                
            case 'Delete':
                if (!this.isEditing) {
                    e.preventDefault();
                    this.deleteRow();
                }
                break;
        }
    }
    
    selectCell(cell) {
        // 清除之前的选择
        if (this.currentCell) {
            this.currentCell.classList.remove('selected');
        }
        
        this.currentCell = cell;
        cell.classList.add('selected');
        
        // 更新当前位置
        const row = cell.closest('tr');
        this.currentRow = parseInt(row.dataset.row);
        this.currentCol = this.getCellColumn(cell);
        
        // 如果是科目单元格，设置为只读
        if (cell.classList.contains('subject-cell')) {
            cell.readOnly = true;
        }
        
        cell.focus();
    }

    getCellColumn(cell) {
        const colMap = {
            'summary': 0,
            'subject': 1,
            'debit': 2,
            'credit': 3
        };
        return colMap[cell.dataset.col] || 0;
    }

    moveToNextCell() {
        const table = document.getElementById('voucher-table');
        const rows = table.querySelectorAll('tbody tr');
        const currentRow = rows[this.currentRow];

        if (!currentRow) return;

        const cells = currentRow.querySelectorAll('.editable-cell');
        let nextCol = this.currentCol + 1;

        if (nextCol >= cells.length) {
            // 移动到下一行的第一个单元格
            this.moveToNextRow();
        } else {
            this.selectCell(cells[nextCol]);
        }
    }

    moveToPreviousCell() {
        const table = document.getElementById('voucher-table');
        const rows = table.querySelectorAll('tbody tr');
        const currentRow = rows[this.currentRow];

        if (!currentRow) return;

        const cells = currentRow.querySelectorAll('.editable-cell');
        let prevCol = this.currentCol - 1;

        if (prevCol < 0) {
            // 移动到上一行的最后一个单元格
            this.moveToPreviousRow();
        } else {
            this.selectCell(cells[prevCol]);
        }
    }

    moveToNextRow() {
        const table = document.getElementById('voucher-table');
        const rows = table.querySelectorAll('tbody tr');

        let nextRow = this.currentRow + 1;

        if (nextRow >= rows.length) {
            // 如果是最后一行，添加新行
            this.addRow();
            return;
        }

        const targetRow = rows[nextRow];
        const cells = targetRow.querySelectorAll('.editable-cell');

        if (cells[this.currentCol]) {
            this.selectCell(cells[this.currentCol]);
        } else if (cells[0]) {
            this.selectCell(cells[0]);
        }
    }

    moveToPreviousRow() {
        const table = document.getElementById('voucher-table');
        const rows = table.querySelectorAll('tbody tr');

        let prevRow = this.currentRow - 1;

        if (prevRow < 0) return;

        const targetRow = rows[prevRow];
        const cells = targetRow.querySelectorAll('.editable-cell');

        if (cells[this.currentCol]) {
            this.selectCell(cells[this.currentCol]);
        } else if (cells[cells.length - 1]) {
            this.selectCell(cells[cells.length - 1]);
        }
    }

    openSubjectSelector(input) {
        const dropdown = input.parentNode.querySelector('.subject-dropdown');
        if (!dropdown) return;

        // 显示下拉框
        dropdown.style.display = 'block';

        // 渲染科目选项
        this.renderSubjectOptions(dropdown, '');

        // 绑定搜索事件
        input.addEventListener('input', (e) => {
            this.renderSubjectOptions(dropdown, e.target.value);
        });

        // 点击外部关闭
        const closeDropdown = (e) => {
            if (!input.parentNode.contains(e.target)) {
                dropdown.style.display = 'none';
                document.removeEventListener('click', closeDropdown);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', closeDropdown);
        }, 100);
    }

    renderSubjectOptions(dropdown, keyword) {
        let filteredSubjects = this.subjects;

        if (keyword) {
            keyword = keyword.toLowerCase();
            filteredSubjects = this.subjects.filter(subject =>
                subject.code.toLowerCase().includes(keyword) ||
                subject.name.toLowerCase().includes(keyword)
            );
        }

        // 限制显示数量
        filteredSubjects = filteredSubjects.slice(0, 20);

        dropdown.innerHTML = filteredSubjects.map(subject => `
            <div class="subject-option" data-subject-id="${subject.id}">
                <span class="subject-code">${subject.code}</span>
                <span class="subject-name">${subject.name}</span>
            </div>
        `).join('');

        // 绑定选择事件
        dropdown.addEventListener('click', (e) => {
            const option = e.target.closest('.subject-option');
            if (option) {
                this.selectSubject(option);
            }
        });
    }

    selectSubject(option) {
        const subjectId = option.dataset.subjectId;
        const subject = this.subjects.find(s => s.id == subjectId);

        if (!subject) return;

        const input = option.closest('.subject-selector').querySelector('.subject-cell');
        const hiddenInput = option.closest('.subject-selector').querySelector('.subject-id');
        const dropdown = option.closest('.subject-dropdown');

        // 更新显示
        input.value = `${subject.code} - ${subject.name}`;
        hiddenInput.value = subject.id;

        // 更新数据
        const row = input.closest('tr');
        const rowIndex = parseInt(row.dataset.row);

        if (rowIndex >= 0 && rowIndex < this.data.length) {
            this.data[rowIndex].subject_id = subject.id;
            this.data[rowIndex].subject = subject;
        }

        // 关闭下拉框
        dropdown.style.display = 'none';

        // 移动到下一个单元格
        this.moveToNextCell();
    }

    setupSubjectAutocomplete() {
        // 为所有科目输入框设置自动完成
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('subject-cell')) {
                e.target.readOnly = false;
                this.openSubjectSelector(e.target);
            }
        });
    }
    
    formatAmount(amount) {
        if (!amount || amount === 0) return '';
        return parseFloat(amount).toFixed(2);
    }
    
    addRow() {
        const newRow = {
            id: null,
            summary: '',
            subject_id: null,
            subject: null,
            debit_amount: 0,
            credit_amount: 0
        };

        this.data.push(newRow);
        this.renderTable();

        // 选中新行的第一个单元格
        setTimeout(() => {
            const lastRowIndex = this.data.length - 1;
            const lastRow = document.querySelector(`tr[data-row="${lastRowIndex}"]`);
            if (lastRow) {
                const firstCell = lastRow.querySelector('.summary-cell');
                if (firstCell) {
                    this.selectCell(firstCell);
                }
            }
        }, 100);
    }

    deleteRow() {
        if (this.currentRow >= 0 && this.currentRow < this.data.length) {
            this.deleteRowByIndex(this.currentRow);
        }
    }

    deleteRowByIndex(index) {
        if (index < 0 || index >= this.data.length) return;

        const rowData = this.data[index];

        // 如果是已保存的行，需要调用API删除
        if (rowData.id) {
            if (!confirm('确定要删除这行分录吗？')) return;

            fetch(`/financial/vouchers/${this.voucherId}/details/${rowData.id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    this.data.splice(index, 1);
                    this.renderTable();
                    this.updateTotals();
                    this.showMessage('删除成功');
                } else {
                    this.showMessage(result.message || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                this.showMessage('删除失败', 'error');
            });
        } else {
            // 未保存的行直接删除
            this.data.splice(index, 1);
            this.renderTable();
            this.updateTotals();
        }
    }

    copyRow() {
        if (this.currentRow >= 0 && this.currentRow < this.data.length) {
            this.clipboard = { ...this.data[this.currentRow] };
            this.clipboard.id = null; // 复制时清除ID
            this.showMessage('行已复制到剪贴板');
        }
    }

    pasteRow() {
        if (!this.clipboard) {
            this.showMessage('剪贴板为空', 'error');
            return;
        }

        const newRow = { ...this.clipboard };
        this.data.push(newRow);
        this.renderTable();
        this.showMessage('行已粘贴');
    }

    handleCellInput(cell) {
        const row = cell.closest('tr');
        const rowIndex = parseInt(row.dataset.row);
        const col = cell.dataset.col;

        if (rowIndex >= 0 && rowIndex < this.data.length) {
            // 更新数据
            if (col === 'summary') {
                this.data[rowIndex].summary = cell.value;
            } else if (col === 'debit') {
                const amount = this.parseAmount(cell.value);
                this.data[rowIndex].debit_amount = amount;
                // 如果输入借方金额，清空贷方金额
                if (amount > 0) {
                    this.data[rowIndex].credit_amount = 0;
                    const creditCell = row.querySelector('.amount-input.credit');
                    if (creditCell) creditCell.value = '';
                }
            } else if (col === 'credit') {
                const amount = this.parseAmount(cell.value);
                this.data[rowIndex].credit_amount = amount;
                // 如果输入贷方金额，清空借方金额
                if (amount > 0) {
                    this.data[rowIndex].debit_amount = 0;
                    const debitCell = row.querySelector('.amount-input.debit');
                    if (debitCell) debitCell.value = '';
                }
            }

            // 实时更新合计
            this.updateTotals();

            // 自动保存
            if (this.options.autoSave) {
                this.debouncedSave(rowIndex);
            }
        }
    }

    parseAmount(value) {
        if (!value || value.trim() === '') return 0;
        const cleaned = value.replace(/[^\d.-]/g, '');
        const amount = parseFloat(cleaned);
        return isNaN(amount) ? 0 : amount;
    }

    debouncedSave = this.debounce((rowIndex) => {
        this.saveRowData(rowIndex);
    }, 1000);

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    saveRowData(rowIndex) {
        if (rowIndex < 0 || rowIndex >= this.data.length) return;

        const rowData = this.data[rowIndex];

        // 验证数据
        if (!rowData.summary || !rowData.subject_id) {
            return; // 数据不完整，不保存
        }

        if (rowData.debit_amount === 0 && rowData.credit_amount === 0) {
            return; // 金额为零，不保存
        }

        const url = rowData.id ?
            `/financial/vouchers/${this.voucherId}/details/${rowData.id}` :
            `/financial/vouchers/${this.voucherId}/details`;

        const method = rowData.id ? 'PUT' : 'POST';

        const data = {
            subject_id: rowData.subject_id,
            summary: rowData.summary,
            debit_amount: rowData.debit_amount,
            credit_amount: rowData.credit_amount
        };

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 更新行数据ID
                if (result.detail_id && !rowData.id) {
                    this.data[rowIndex].id = result.detail_id;
                }
                this.showMessage('保存成功');
            } else {
                this.showMessage(result.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            this.showMessage('保存失败', 'error');
        });
    }

    updateTotals() {
        let debitTotal = 0;
        let creditTotal = 0;

        this.data.forEach(row => {
            debitTotal += row.debit_amount || 0;
            creditTotal += row.credit_amount || 0;
        });

        const debitTotalEl = document.getElementById('debit-total');
        const creditTotalEl = document.getElementById('credit-total');
        const amountWordsEl = document.getElementById('amount-words-text');

        if (debitTotalEl) debitTotalEl.textContent = debitTotal.toFixed(2);
        if (creditTotalEl) creditTotalEl.textContent = creditTotal.toFixed(2);

        // 更新大写金额
        if (amountWordsEl) {
            const maxAmount = Math.max(debitTotal, creditTotal);
            amountWordsEl.textContent = this.numberToChinese(maxAmount);
        }

        // 更新平衡指示器
        this.updateBalanceIndicator(debitTotal, creditTotal);
    }

    updateBalanceIndicator(debitTotal, creditTotal) {
        const indicator = document.getElementById('balance-indicator');
        if (!indicator) return;

        const diff = Math.abs(debitTotal - creditTotal);

        if (diff < 0.01) {
            indicator.textContent = '借贷平衡';
            indicator.className = 'balance-indicator balanced';
        } else {
            indicator.textContent = `不平衡 (差额: ${diff.toFixed(2)})`;
            indicator.className = 'balance-indicator unbalanced';
        }
    }

    numberToChinese(num) {
        // 简化的数字转中文实现
        if (num === 0) return '零元整';

        const units = ['', '十', '百', '千', '万'];
        const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

        const yuan = Math.floor(num);
        const jiao = Math.floor((num - yuan) * 10);
        const fen = Math.floor((num - yuan - jiao / 10) * 100);

        if (yuan === 0) {
            if (jiao === 0) {
                return fen > 0 ? `${digits[fen]}分` : '零元整';
            } else {
                return fen > 0 ? `${digits[jiao]}角${digits[fen]}分` : `${digits[jiao]}角整`;
            }
        }

        // 简化处理，只处理万以内的数字
        let result = '';
        if (yuan < 10000) {
            result = yuan.toString().split('').map((digit, index, arr) => {
                const unitIndex = arr.length - index - 1;
                return digit === '0' ? '' : digits[parseInt(digit)] + units[unitIndex];
            }).join('');
        } else {
            result = yuan.toString(); // 超过万的直接显示数字
        }

        result += '元';

        if (jiao > 0) {
            result += digits[jiao] + '角';
        }
        if (fen > 0) {
            result += digits[fen] + '分';
        }
        if (jiao === 0 && fen === 0) {
            result += '整';
        }

        return result;
    }

    checkBalance() {
        this.updateTotals();

        let debitTotal = 0;
        let creditTotal = 0;

        this.data.forEach(row => {
            debitTotal += row.debit_amount || 0;
            creditTotal += row.credit_amount || 0;
        });

        const diff = Math.abs(debitTotal - creditTotal);

        if (diff < 0.01) {
            this.showMessage('借贷平衡检查通过！');
            return true;
        } else {
            this.showMessage(`借贷不平衡，差额: ${diff.toFixed(2)}元`, 'error');
            return false;
        }
    }

    saveVoucher() {
        if (!this.checkBalance()) {
            return;
        }

        // 保存所有未保存的行
        const unsavedRows = this.data.filter((row, index) => {
            return !row.id && row.summary && row.subject_id &&
                   (row.debit_amount > 0 || row.credit_amount > 0);
        });

        if (unsavedRows.length > 0) {
            this.showMessage('正在保存未保存的分录...');

            Promise.all(unsavedRows.map((row, index) => {
                const rowIndex = this.data.indexOf(row);
                return this.saveRowData(rowIndex);
            })).then(() => {
                this.showMessage('凭证保存成功！');
            }).catch(() => {
                this.showMessage('部分分录保存失败', 'error');
            });
        } else {
            this.showMessage('凭证保存成功！');
        }
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show`;
        toast.style.position = 'fixed';
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 导出给全局使用
window.ProfessionalVoucherEditor = ProfessionalVoucherEditor;
