2025-06-08 12:49:28,942 INFO: 应用启动 - PID: 24860 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-08 12:49:42,947 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                (?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?, ?)
            ]
[parameters: ('PZ20250608001', datetime.date(2025, 6, 8), 42, '入库凭证', '杀杀杀', Decimal('0.00'), '草稿', '手工录入', None, 0, 34, None, None, None, None, '梦梦梦梦梦梦梦梦梦梦梦梦梦梦')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:157]
2025-06-08 12:49:42,948 ERROR: 详细错误信息: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py", line 136, in create_voucher
    db.session.execute(insert_sql, params)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\scoping.py", line 778, in execute
    return self._proxied.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2245, in _execute_internal
    result = conn.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                (?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?, ?)
            ]
[parameters: ('PZ20250608001', datetime.date(2025, 6, 8), 42, '入库凭证', '杀杀杀', Decimal('0.00'), '草稿', '手工录入', None, 0, 34, None, None, None, None, '梦梦梦梦梦梦梦梦梦梦梦梦梦梦')]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:158]
2025-06-08 13:32:40,674 ERROR: 创建财务凭证失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                (?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?, ?)
            ]
[parameters: ('PZ20250608001', datetime.date(2025, 6, 8), 42, '入库凭证', '杀杀杀', Decimal('0.00'), '草稿', '手工录入', None, 0, 34, None, None, None, None, 'WWWWWWWWWWWWWWWW')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:157]
2025-06-08 13:32:40,675 ERROR: 详细错误信息: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py", line 136, in create_voucher
    'posted_by': None,
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\scoping.py", line 778, in execute
    return self._proxied.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2245, in _execute_internal
    result = conn.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                (?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?,
                 ?, ?, ?, ?, ?, ?)
            ]
[parameters: ('PZ20250608001', datetime.date(2025, 6, 8), 42, '入库凭证', '杀杀杀', Decimal('0.00'), '草稿', '手工录入', None, 0, 34, None, None, None, None, 'WWWWWWWWWWWWWWWW')]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\vouchers.py:158]
