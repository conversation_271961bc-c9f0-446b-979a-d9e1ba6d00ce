msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: sk\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Chyby"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Čas"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Účet"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Príjemca"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Výkaz ziskov a strát"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Súvaha"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
#, fuzzy
msgid "Trial Balance"
msgstr "Hrubá súvaha"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Účtovná kniha"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
#, fuzzy
msgid "Query"
msgstr "Dotaz"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
#, fuzzy
msgid "Holdings"
msgstr "Vlastníctvo"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Komodity"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Udalosti"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Editor"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Možnosti"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Štatistiky"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Pomoc"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Dátum"

#: src/fava/templates/_journal_table.html:49
#, fuzzy
msgid "F"
msgstr "V"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Cena"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Výdavok"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Zmena"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Zostatok"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Uložiť"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Ukladá sa..."

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Súbor bol zmenený. Kliknutie obnoví stránku."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Rozšíriť všetky účty"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Rozšíriť všetko"

#: frontend/src/reports/accounts/AccountReport.svelte:45
#, fuzzy
msgid "Account Journal"
msgstr "Register účtu"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Zmeny"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Zostatky"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Ukázať zdroj %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Súbor"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Riadok"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Chyba"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Žiadne chyby."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Udalosť: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Popis"

#: src/fava/templates/help.html:8
#, fuzzy
msgid "Help pages"
msgstr "Pomocné stránky"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Mena"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Mena výdavku"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Majetok podľa"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Čistý zisk"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Príjem"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Výdavky"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Kľúč"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Hodnota"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Stiahnuť ako"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Položky na účte"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Súhrn"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Aktualizovať aktivitu"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Kliknutie skopíruje vyrovnávacie príkazy pre účty (okrem účtov oznčených zelenou farbou)."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Kopírovať vyrovnávacie príkazy"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Posledný zápis"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Zápisy podľa typu"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Typ"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "Počet zápisov"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Popis"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Číslo"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Importovať"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Zápisý rozpočtov"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Zdroj"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Extraktovať"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "...zadajte BQL dotaz. 'help' zobrazí všetky dostupné príkazy."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Metadáta"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Pridať metadáta"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Poznámka"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Poloha"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Kontext"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Zostatky pred zápisom"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Zostatky po zápise"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Vyriešené transakcie"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Nevyriešené transakcie"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Ostatné transakcie"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Dokumenty s tagom #discovered (objavené)"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Položky"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Nahrať súbor-y"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Nahrať"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Zarovnať hodnoty"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Prepnúť kommentár (výber)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Rozšíriť všetko"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Zhrnúť všetko"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Fava možnosti"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "pomoc"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Beancount možnosti"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Potvrdiť"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Držte Shift počas kliknutia na rožšírenie všetkých podpodložiek. Držte Ctrl alebo Cmd počas kliknutia na rožirenie jedného stupňa."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Exportovať"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Filtrovať podľa tagu, príjemcu, ..."

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Stiahnuť aktuálne filtrované zápisy ako Beancount súbor"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Ročne"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Kvartálne"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Mesačne"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Týždenne"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Denne"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Upraviť"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Suma"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Dokumenty s tagom #linked"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr ""

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr ""

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr ""

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr ""

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr ""

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr ""

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr ""

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr ""

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr ""

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr ""

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr ""

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr ""

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr ""

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr ""

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr ""

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr ""

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr ""

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr ""

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr ""

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr ""

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr ""

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr ""

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr ""

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr ""

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr ""

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr ""

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

