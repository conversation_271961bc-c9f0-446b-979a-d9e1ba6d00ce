msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: bg\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Грешки"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Време"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Сметка"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Бенефициент"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Отчет за Доходи"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Баланси"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Оборотна Ведомост"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Архив"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Търсене"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Стопанства"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Стоки"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Събития"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Редактор"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Настройки"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Статистика"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Помощ"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Дата"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "Ф"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Цена"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Разходи"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Промяна"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Баланс"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Запази"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Запазване..."

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Файла е променен. Кликнете, за да презаредите."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Разгъни всички сметки"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Разгъни всички"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Счетоводен Дневник"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Промени"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Баланс"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Показване на %(file)s:%(lineno)s на източника"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Файл"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Ред"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Грешка"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Няма грешки."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Събитие: %(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Описание"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Помощни страници"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Валута"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Валута на разходите"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Стопанства от"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Чиста Печалба"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Доход"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Разходи"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Ключ"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Стойност"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Изтегли като"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Публикации по Сметка"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Общо"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Обновления"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Кликнете, за да копирате балансовите директивите на сметките (с изключение на зелените) в клипборда."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Копирай балансовите директивите"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Последен Запис"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Записи по Тип"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Тип"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "Записи"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Основание"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Количество"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Внасяне"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Бюджетни вписвания"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Източник"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Извадка"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "... въведи BQL заявка. 'help' за изброяване на наличните команди."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Метаданни"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Добави метаданни"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Бележка"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Положение"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Контекст"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Баланс преди запис"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Баланс след запис"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Потвърдени транзакции"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Непотвърдени транзакции"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Други транзакции"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Документи с #discovered таг"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Публикации"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Качване на файл(ове)"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Качване"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Подравняване на празното пространство между сумите"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Превключи коментар (на избраното)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Разпъни всички категории"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Събери всички категории"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Fava настройки"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "помощ"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Beancount настройки"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Изпрати"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Задръжте Shift, докато кликате, за да разширите всички поднива.\n"
"Задръжте Ctrl или Cmd, докато кликате, за да разширите едно ниво."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Изнасяне"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Филтриране по таг, бенефициент, ..."

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Изтеглете филтрираните в момента записи като Beancount файл"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Годишен"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Тримесечен"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Месечен"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Седмичен"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Дневен"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Редактиране"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Сума"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Документи с #linked таг"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "На Цена"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "По Пазарна Стойност"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "Единици"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "Дървовидна диаграма"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "Пръстеновиднa диаграмa"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "Трябва да е една от декларираните сметки"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "Предишен"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "Следващ"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "Транзакция"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "Добави"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "Преместване или преименуване на документ"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "Име"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "Документи"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "Изтрий"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "Линейна диаграма"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "Площна диаграма"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "Продължи"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "Изчисти"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "Отвори сметката"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr "Да се изтрие ли този файл?"

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr "Не бяха намерени файлове за внасяне."

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr "Превключи %(type)s записи"

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr "Преобразуване в %(currency)s"

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr "Стълбовидна диаграма с натрупване"

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr "Стълбовидна диаграма без натрупване"

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr "Диаграмата е празна."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr "Превключи метаданни"

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr "Превключи публикации"

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr "Нетна Стойност"

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr "Баланс по сметката"

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr "презареди"

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr "продължи"

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr "Изтриване..."

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

