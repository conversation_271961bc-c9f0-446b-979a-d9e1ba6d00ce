{% extends "financial/base.html" %}

{% block title %}总账查询{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">总账查询</h3>
                </div>
                <div class="card-body">
                    <!-- 查询条件 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ start_date }}" required>
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" 
                                       value="{{ end_date }}" required>
                            </div>
                            <div class="col-md-3">
                                <label for="subject_type" class="form-label">科目类型</label>
                                <select class="form-select" id="subject_type" name="subject_type">
                                    <option value="">全部类型</option>
                                    <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                                    <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                                    <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                                    <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                                    <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="exportGeneralLedger()">
                                        <i class="fas fa-file-excel"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 总账汇总表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>科目编码</th>
                                    <th>科目名称</th>
                                    <th>科目类型</th>
                                    <th>余额方向</th>
                                    <th class="text-right">期初余额</th>
                                    <th class="text-right">本期借方</th>
                                    <th class="text-right">本期贷方</th>
                                    <th class="text-right">期末余额</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set total_opening = 0 %}
                                {% set total_debit = 0 %}
                                {% set total_credit = 0 %}
                                {% set total_ending = 0 %}
                                
                                {% for item in general_ledger_data %}
                                {% set total_opening = total_opening + item.opening_balance %}
                                {% set total_debit = total_debit + item.period_debit %}
                                {% set total_credit = total_credit + item.period_credit %}
                                {% set total_ending = total_ending + item.ending_balance %}
                                
                                <tr>
                                    <td>{{ item.code }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ item.subject_type }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {% if item.balance_direction == '借方' %}bg-primary{% else %}bg-warning{% endif %}">
                                            {{ item.balance_direction }}
                                        </span>
                                    </td>
                                    <td class="text-right">
                                        {% if item.opening_balance != 0 %}
                                            {{ "%.2f"|format(item.opening_balance) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if item.period_debit > 0 %}
                                            {{ "%.2f"|format(item.period_debit) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if item.period_credit > 0 %}
                                            {{ "%.2f"|format(item.period_credit) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if item.ending_balance != 0 %}
                                            <strong>{{ "%.2f"|format(item.ending_balance) }}</strong>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('financial.detail_ledger', subject_id=item.id, start_date=start_date, end_date=end_date) }}" 
                                           class="btn btn-sm btn-outline-primary" title="查看明细账">
                                            <i class="fas fa-list"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not general_ledger_data %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        没有找到相关数据
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                            
                            {% if general_ledger_data %}
                            <tfoot class="table-secondary">
                                <tr>
                                    <th colspan="4">合计</th>
                                    <th class="text-right">{{ "%.2f"|format(total_opening) }}</th>
                                    <th class="text-right">{{ "%.2f"|format(total_debit) }}</th>
                                    <th class="text-right">{{ "%.2f"|format(total_credit) }}</th>
                                    <th class="text-right">{{ "%.2f"|format(total_ending) }}</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                            {% endif %}
                        </table>
                    </div>

                    <!-- 试算平衡检查 -->
                    {% if general_ledger_data %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>试算平衡检查</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>本期借方发生额合计：</strong>{{ "%.2f"|format(total_debit) }}</p>
                                            <p><strong>本期贷方发生额合计：</strong>{{ "%.2f"|format(total_credit) }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            {% set balance_diff = total_debit - total_credit %}
                                            <p><strong>借贷差额：</strong>
                                                <span class="{% if balance_diff == 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ "%.2f"|format(balance_diff) }}
                                                </span>
                                            </p>
                                            <p>
                                                {% if balance_diff == 0 %}
                                                    <span class="badge bg-success">✓ 试算平衡</span>
                                                {% else %}
                                                    <span class="badge bg-danger">✗ 试算不平衡</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 按科目类型汇总 -->
                    {% if general_ledger_data %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>按科目类型汇总</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>科目类型</th>
                                                    <th class="text-right">期末余额</th>
                                                    <th class="text-right">科目数量</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set type_summary = {} %}
                                                {% for item in general_ledger_data %}
                                                    {% if item.subject_type not in type_summary %}
                                                        {% set _ = type_summary.update({item.subject_type: {'balance': 0, 'count': 0}}) %}
                                                    {% endif %}
                                                    {% set _ = type_summary[item.subject_type].update({
                                                        'balance': type_summary[item.subject_type]['balance'] + item.ending_balance,
                                                        'count': type_summary[item.subject_type]['count'] + 1
                                                    }) %}
                                                {% endfor %}
                                                
                                                {% for type_name, summary in type_summary.items() %}
                                                <tr>
                                                    <td>{{ type_name }}</td>
                                                    <td class="text-right">{{ "%.2f"|format(summary.balance) }}</td>
                                                    <td class="text-right">{{ summary.count }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
function exportGeneralLedger() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;
    
    const url = `{{ url_for('financial.export_report', report_type='general_ledger') }}?start_date=${startDate}&end_date=${endDate}&subject_type=${subjectType}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
