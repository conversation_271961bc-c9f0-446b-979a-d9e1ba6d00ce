2025-06-08 19:26:40,194 INFO: 应用启动 - PID: 30472 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:815]
2025-06-08 19:29:22,543 ERROR: 清理重复会计科目失败: (pyodbc.IntegrityError) ('23000', "[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]当 IDENTITY_INSERT 设置为 OFF 时，不能为表 'administrative_areas' 中的标识列插入显式值。 (544) (SQLExecDirectW)")
[SQL: 
                INSERT INTO administrative_areas
                (id, name, code, level, parent_id, description, status, is_township_school)
                VALUES (1, '系统', 'SYS', 1, NULL, '系统会计科目专用区域', 1, 0)
            ]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\financial\accounting_subjects.py:729]
2025-06-08 19:34:50,907 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-08 19:34:50,933 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
