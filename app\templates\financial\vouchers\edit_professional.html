{% extends 'base.html' %}

{% block title %}专业凭证编辑器 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .voucher-editor-container {
    margin-top: 20px;
  }
  .voucher-card {
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
  }
  .voucher-table th, .voucher-table td {
    vertical-align: middle;
  }
  .voucher-table input[type="number"], .voucher-table textarea {
    transition: all 0.3s;
  }
  .voucher-table input[type="number"]:focus, .voucher-table textarea:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background-color: #fff9f9;
  }
  .voucher-row:hover {
    background-color: #f8f9fa;
  }
  .voucher-row td {
    padding: 8px;
  }
  .balance-indicator {
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 4px;
    margin-left: 10px;
  }
  .balance-indicator.text-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  .balance-indicator.text-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  .balance-indicator.text-secondary {
    background-color: #e2e3e5;
    color: #6c757d;
    border: 1px solid #d6d8db;
  }
  .subject-select {
    max-width: 100%;
  }
  .toolbar-buttons {
    margin-bottom: 20px;
  }
  .shortcut-help {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 15px;
    border-radius: 5px;
    z-index: 1000;
    max-width: 300px;
    font-size: 12px;
    display: none;
  }
  .shortcut-help h6 {
    color: #ffc107;
    margin-bottom: 10px;
  }
  .shortcut-help .shortcut-item {
    margin-bottom: 5px;
  }
  .shortcut-help .key {
    background: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
  }
</style>
<style>
/* 页面特定样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.voucher-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.info-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.info-value {
    color: #6c757d;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.status-draft { background: #fff3cd; color: #856404; }
.status-pending { background: #d1ecf1; color: #0c5460; }
.status-approved { background: #d4edda; color: #155724; }
.status-posted { background: #e2e3e5; color: #383d41; }

.shortcut-help {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1000;
    max-width: 300px;
}

.shortcut-help h6 {
    color: #ffc107;
    margin-bottom: 10px;
}

.shortcut-help .shortcut-item {
    margin-bottom: 5px;
}

.shortcut-help .key {
    background: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- 消息容器 -->
  <div id="alertContainer"></div>

  <div class="row">
    <div class="col-md-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">
            专业凭证编辑器 - {{ voucher.voucher_number }}
            {% if voucher.status == '已审核' %}
              <span class="badge badge-success">已审核</span>
            {% else %}
              <span class="badge badge-warning">{{ voucher.status }}</span>
            {% endif %}
          </h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
              <div class="dropdown-header">操作:</div>
              <a class="dropdown-item" href="#" id="addRowBtn"><i class="fas fa-plus fa-sm fa-fw mr-2 text-gray-400"></i>添加行</a>
              <a class="dropdown-item" href="#" id="deleteSelectedBtn"><i class="fas fa-trash fa-sm fa-fw mr-2 text-gray-400"></i>删除选中行</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="#" id="checkBalanceBtn"><i class="fas fa-balance-scale fa-sm fa-fw mr-2 text-gray-400"></i>检查平衡</a>
              <a class="dropdown-item" href="#" id="saveVoucherBtn"><i class="fas fa-save fa-sm fa-fw mr-2 text-gray-400"></i>保存凭证</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="{{ url_for('financial.view_voucher', id=voucher.id) }}"><i class="fas fa-eye fa-sm fa-fw mr-2 text-gray-400"></i>查看凭证</a>
              <a class="dropdown-item" href="{{ url_for('financial.edit_voucher', id=voucher.id) }}"><i class="fas fa-edit fa-sm fa-fw mr-2 text-gray-400"></i>标准编辑器</a>
              <a class="dropdown-item" href="{{ url_for('financial.vouchers_index') }}"><i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>返回列表</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 凭证基本信息 -->
          <div class="card mb-4">
            <div class="card-header bg-light">
              <h6 class="mb-0 text-primary"><i class="fas fa-info-circle"></i> 凭证基本信息</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label for="voucherType">凭证类型</label>
                    <select class="form-control" id="voucherType">
                      <option value="收款凭证" {{ 'selected' if voucher.voucher_type == '收款凭证' else '' }}>收款凭证</option>
                      <option value="付款凭证" {{ 'selected' if voucher.voucher_type == '付款凭证' else '' }}>付款凭证</option>
                      <option value="转账凭证" {{ 'selected' if voucher.voucher_type == '转账凭证' else '' }}>转账凭证</option>
                    </select>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label for="voucherDate">凭证日期</label>
                    <input type="date" class="form-control" id="voucherDate"
                           value="{{ voucher.voucher_date.strftime('%Y-%m-%d') }}">
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label for="attachmentCount">附件数量</label>
                    <input type="number" class="form-control" id="attachmentCount"
                           value="{{ voucher.attachment_count or 0 }}" min="0">
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label>凭证状态</label>
                    <div class="form-control-plaintext">
                      <span class="badge badge-{{ 'success' if voucher.status == '已审核' else 'warning' }}">
                        {{ voucher.status }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 专业编辑器容器 -->
          <div class="voucher-editor-container">
            <div class="voucher-table-section">
              <!-- 编辑器将在这里渲染 -->
              <div id="loading-indicator" style="text-align: center; padding: 20px; color: #666;">
                <i class="fas fa-spinner fa-spin"></i> 正在加载编辑器...
              </div>
            </div>
          </div>

          <!-- 签字区域 -->
          <div class="voucher-signature-section mt-4">
            <div class="row">
              <div class="col-md-3">
                <div class="signature-item">
                  <span class="signature-label">制单：</span>
                  <span class="signature-box">{{ voucher.created_by.username if voucher.created_by else '' }}</span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="signature-item">
                  <span class="signature-label">审核：</span>
                  <span class="signature-box">{{ voucher.reviewed_by.username if voucher.reviewed_by else '' }}</span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="signature-item">
                  <span class="signature-label">记账：</span>
                  <span class="signature-box">{{ voucher.posted_by.username if voucher.posted_by else '' }}</span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="signature-item">
                  <span class="signature-label">出纳：</span>
                  <span class="signature-box"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 快捷键帮助 -->
<div class="shortcut-help" id="shortcut-help">
    <h6><i class="fas fa-keyboard"></i> 快捷键帮助</h6>
    <div class="shortcut-item"><span class="key">Tab</span> 下一个单元格</div>
    <div class="shortcut-item"><span class="key">Shift+Tab</span> 上一个单元格</div>
    <div class="shortcut-item"><span class="key">Enter</span> 下一行</div>
    <div class="shortcut-item"><span class="key">↑↓</span> 上下移动</div>
    <div class="shortcut-item"><span class="key">Ctrl+N</span> 添加新行</div>
    <div class="shortcut-item"><span class="key">Del</span> 删除行</div>
    <div class="shortcut-item"><span class="key">Ctrl+C</span> 复制行</div>
    <div class="shortcut-item"><span class="key">Ctrl+V</span> 粘贴行</div>
    <div class="shortcut-item"><span class="key">Ctrl+S</span> 保存凭证</div>
    <div class="shortcut-item"><span class="key">F9</span> 检查平衡</div>
    <div class="shortcut-item"><span class="key">F1</span> 显示/隐藏帮助</div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
$(document).ready(function() {
    console.log('专业编辑器页面加载完成');

    // 移除加载指示器
    $('#loading-indicator').hide();

    // 初始化编辑器表格
    initVoucherEditor();

    // 绑定基本信息保存事件
    $('#voucherType, #voucherDate, #attachmentCount').on('change', saveBasicInfo);

    // F1 显示/隐藏快捷键帮助
    $(document).on('keydown', function(e) {
        if (e.key === 'F1') {
            e.preventDefault();
            toggleShortcutHelp();
        }
    });

    // 默认显示快捷键帮助3秒
    setTimeout(function() {
        $('#shortcut-help').show();
        setTimeout(function() {
            $('#shortcut-help').hide();
        }, 3000);
    }, 1000);
});

function initVoucherEditor() {
    console.log('初始化凭证编辑器');

    // 创建工具栏
    const toolbar = `
        <div class="voucher-toolbar mb-3">
            <button type="button" class="btn btn-success btn-sm" onclick="addVoucherRow()">
                <i class="fas fa-plus"></i> 添加行
            </button>
            <button type="button" class="btn btn-danger btn-sm" onclick="deleteSelectedRows()">
                <i class="fas fa-trash"></i> 删除选中行
            </button>
            <button type="button" class="btn btn-primary btn-sm" onclick="saveVoucher()">
                <i class="fas fa-save"></i> 保存凭证
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="checkBalance()">
                <i class="fas fa-balance-scale"></i> 检查平衡
            </button>
            <span class="balance-indicator ml-3" id="balance-indicator">未检查</span>
        </div>
    `;

    // 创建表格
    const table = `
        <div class="table-responsive">
            <table class="table table-bordered voucher-editor-table" id="voucher-table">
                <thead class="thead-dark">
                    <tr>
                        <th width="5%">选择</th>
                        <th width="5%">行号</th>
                        <th width="25%">摘要</th>
                        <th width="30%">会计科目</th>
                        <th width="15%">借方金额</th>
                        <th width="15%">贷方金额</th>
                        <th width="5%">操作</th>
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <td colspan="4"><strong>合计：</strong></td>
                        <td class="text-right"><strong id="debit-total">0.00</strong></td>
                        <td class="text-right"><strong id="credit-total">0.00</strong></td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    // 插入到页面
    $('.voucher-table-section').html(toolbar + table);

    // 加载凭证数据
    loadVoucherData();
}

function loadVoucherData() {
    console.log('加载凭证数据');

    $.ajax({
        url: `/financial/vouchers/{{ voucher.id }}/details`,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                console.log('凭证数据加载成功:', response.details);
                renderVoucherRows(response.details);
                updateTotals();
            } else {
                console.error('加载凭证数据失败:', response.message);
                showMessage('加载凭证数据失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('加载凭证数据失败:', error);
            showMessage('加载凭证数据失败，请刷新页面重试', 'danger');
        }
    });
}

function renderVoucherRows(details) {
    const tbody = $('#voucher-tbody');
    tbody.empty();

    if (details.length === 0) {
        // 添加一个空行
        addVoucherRow();
        return;
    }

    details.forEach(function(detail, index) {
        const row = createVoucherRow(detail, index + 1);
        tbody.append(row);
    });
}

function createVoucherRow(detail, lineNumber) {
    detail = detail || {};

    const subjectDisplay = detail.subject ?
        `${detail.subject.code} - ${detail.subject.name}` : '';

    return `
        <tr data-detail-id="${detail.id || ''}" data-line-number="${lineNumber}">
            <td class="text-center">
                <input type="checkbox" class="row-checkbox">
            </td>
            <td class="text-center line-number">${lineNumber}</td>
            <td>
                <textarea class="form-control form-control-sm summary-input"
                          rows="1" placeholder="输入摘要...">${detail.summary || ''}</textarea>
            </td>
            <td>
                <div class="input-group">
                    <input type="text" class="form-control form-control-sm subject-input"
                           placeholder="选择会计科目..." value="${subjectDisplay}" readonly>
                    <input type="hidden" class="subject-id" value="${detail.subject_id || ''}">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="selectSubject(this)">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm debit-input"
                       step="0.01" min="0" placeholder="0.00"
                       value="${detail.debit_amount || ''}" onchange="updateTotals()">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm credit-input"
                       step="0.01" min="0" placeholder="0.00"
                       value="${detail.credit_amount || ''}" onchange="updateTotals()">
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </td>
        </tr>
    `;
}

function addVoucherRow() {
    const tbody = $('#voucher-tbody');
    const lineNumber = tbody.find('tr').length + 1;
    const row = createVoucherRow({}, lineNumber);
    tbody.append(row);
    updateLineNumbers();
}

function deleteRow(button) {
    $(button).closest('tr').remove();
    updateLineNumbers();
    updateTotals();
}

function deleteSelectedRows() {
    $('.row-checkbox:checked').closest('tr').remove();
    updateLineNumbers();
    updateTotals();
}

function updateLineNumbers() {
    $('#voucher-tbody tr').each(function(index) {
        $(this).find('.line-number').text(index + 1);
        $(this).attr('data-line-number', index + 1);
    });
}

function updateTotals() {
    let debitTotal = 0;
    let creditTotal = 0;

    $('.debit-input').each(function() {
        const value = parseFloat($(this).val()) || 0;
        debitTotal += value;
    });

    $('.credit-input').each(function() {
        const value = parseFloat($(this).val()) || 0;
        creditTotal += value;
    });

    $('#debit-total').text(debitTotal.toFixed(2));
    $('#credit-total').text(creditTotal.toFixed(2));

    // 更新平衡指示器
    const diff = Math.abs(debitTotal - creditTotal);
    const indicator = $('#balance-indicator');

    if (diff < 0.01) {
        indicator.text('借贷平衡').removeClass('text-danger').addClass('text-success');
    } else {
        indicator.text(`不平衡 (差额: ${diff.toFixed(2)})`).removeClass('text-success').addClass('text-danger');
    }
}

function checkBalance() {
    updateTotals();
    const indicator = $('#balance-indicator');
    if (indicator.hasClass('text-success')) {
        showMessage('借贷平衡检查通过', 'success');
    } else {
        showMessage('借贷不平衡，请检查金额', 'warning');
    }
}

function selectSubject(button) {
    // 这里可以实现科目选择功能
    showMessage('科目选择功能开发中...', 'info');
}

function saveVoucher() {
    showMessage('保存功能开发中...', 'info');
}

function saveBasicInfo() {
    const data = {
        voucher_type: $('#voucherType').val(),
        voucher_date: $('#voucherDate').val(),
        attachment_count: parseInt($('#attachmentCount').val()) || 0
    };

    $.ajax({
        url: `/financial/vouchers/{{ voucher.id }}`,
        type: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(result) {
            if (result.success) {
                showMessage('基本信息保存成功', 'success');
            } else {
                showMessage(result.message || '保存失败', 'danger');
            }
        },
        error: function() {
            showMessage('保存失败', 'danger');
        }
    });
}

function toggleShortcutHelp() {
    $('#shortcut-help').toggle();
}

function showMessage(message, type) {
    const alertClass = type === 'danger' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    // 移除旧的提示
    $('.alert').remove();

    // 添加新提示到页面顶部
    $('#alertContainer').html(alert);

    // 3秒后自动移除
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
