{% extends "base.html" %}

{% block title %}编辑凭证 - 专业编辑器{% endblock %}

{% block extra_css %}
<!-- 财务模块基础样式 -->
<style>
    .financial-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 1.5rem;
    }
</style>
<!-- 专业编辑器样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/voucher-editor.css') }}">
<style>
/* 页面特定样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.voucher-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.info-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.info-value {
    color: #6c757d;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.status-draft { background: #fff3cd; color: #856404; }
.status-pending { background: #d1ecf1; color: #0c5460; }
.status-approved { background: #d4edda; color: #155724; }
.status-posted { background: #e2e3e5; color: #383d41; }

.shortcut-help {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1000;
    max-width: 300px;
}

.shortcut-help h6 {
    color: #ffc107;
    margin-bottom: 10px;
}

.shortcut-help .shortcut-item {
    margin-bottom: 5px;
}

.shortcut-help .key {
    background: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">记账凭证</a></li>
        <li class="breadcrumb-item active">专业编辑器</li>
    </ol>
</nav>
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="fas fa-edit"></i> 编辑记账凭证</h2>
                <p class="mb-0">凭证号：{{ voucher.voucher_number }} | 专业编辑器模式</p>
            </div>
            <div>
                <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-light">
                    <i class="fas fa-eye"></i> 查看凭证
                </a>
                <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-outline-light">
                    <i class="fas fa-list"></i> 返回列表
                </a>
            </div>
        </div>
    </div>

    <!-- 凭证基本信息 -->
    <div class="voucher-info-grid">
        <div class="info-item">
            <div class="info-label">凭证类型</div>
            <div class="info-value">
                <select class="form-control form-control-sm" id="voucherType">
                    <option value="收款凭证" {{ 'selected' if voucher.voucher_type == '收款凭证' }}>收款凭证</option>
                    <option value="付款凭证" {{ 'selected' if voucher.voucher_type == '付款凭证' }}>付款凭证</option>
                    <option value="转账凭证" {{ 'selected' if voucher.voucher_type == '转账凭证' }}>转账凭证</option>
                </select>
            </div>
        </div>
        
        <div class="info-item">
            <div class="info-label">凭证日期</div>
            <div class="info-value">
                <input type="date" class="form-control form-control-sm" id="voucherDate" 
                       value="{{ voucher.voucher_date.strftime('%Y-%m-%d') }}">
            </div>
        </div>
        
        <div class="info-item">
            <div class="info-label">附件数量</div>
            <div class="info-value">
                <input type="number" class="form-control form-control-sm" id="attachmentCount" 
                       value="{{ voucher.attachment_count or 0 }}" min="0">
            </div>
        </div>
        
        <div class="info-item">
            <div class="info-label">凭证状态</div>
            <div class="info-value">
                <span class="status-badge status-{{ voucher.status.lower().replace('草稿', 'draft').replace('待审核', 'pending').replace('已审核', 'approved').replace('已记账', 'posted') }}">
                    {{ voucher.status }}
                </span>
            </div>
        </div>
    </div>

    <!-- 专业编辑器容器 -->
    <div class="voucher-editor-container">
        <div class="voucher-table-section">
            <!-- 编辑器将在这里渲染 -->
            <div id="loading-indicator" style="text-align: center; padding: 20px; color: #666;">
                <i class="fas fa-spinner fa-spin"></i> 正在加载编辑器...
            </div>
            <div id="debug-info" style="background: #f8f9fa; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 12px; display: none;">
                <strong>调试信息：</strong><br>
                <div id="debug-messages"></div>
            </div>
        </div>
    </div>

    <!-- 签字区域 -->
    <div class="voucher-signature-section mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="signature-item">
                    <span class="signature-label">制单：</span>
                    <span class="signature-box">{{ voucher.created_by.username if voucher.created_by else '' }}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="signature-item">
                    <span class="signature-label">审核：</span>
                    <span class="signature-box">{{ voucher.reviewed_by.username if voucher.reviewed_by else '' }}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="signature-item">
                    <span class="signature-label">记账：</span>
                    <span class="signature-box">{{ voucher.posted_by.username if voucher.posted_by else '' }}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="signature-item">
                    <span class="signature-label">出纳：</span>
                    <span class="signature-box"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快捷键帮助 -->
<div class="shortcut-help" id="shortcut-help">
    <h6><i class="fas fa-keyboard"></i> 快捷键帮助</h6>
    <div class="shortcut-item"><span class="key">Tab</span> 下一个单元格</div>
    <div class="shortcut-item"><span class="key">Shift+Tab</span> 上一个单元格</div>
    <div class="shortcut-item"><span class="key">Enter</span> 下一行</div>
    <div class="shortcut-item"><span class="key">↑↓</span> 上下移动</div>
    <div class="shortcut-item"><span class="key">Ctrl+N</span> 添加新行</div>
    <div class="shortcut-item"><span class="key">Del</span> 删除行</div>
    <div class="shortcut-item"><span class="key">Ctrl+C</span> 复制行</div>
    <div class="shortcut-item"><span class="key">Ctrl+V</span> 粘贴行</div>
    <div class="shortcut-item"><span class="key">Ctrl+S</span> 保存凭证</div>
    <div class="shortcut-item"><span class="key">F9</span> 检查平衡</div>
    <div class="shortcut-item"><span class="key">F1</span> 显示/隐藏帮助</div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入专业编辑器 -->
<script src="{{ url_for('static', filename='financial/js/professional-voucher-editor.js') }}"></script>

<script>
// 页面初始化
let voucherEditor;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化专业编辑器');

    // 检查必要的元素
    const container = document.querySelector('.voucher-editor-container');
    const tableSection = document.querySelector('.voucher-table-section');

    console.log('编辑器容器:', container);
    console.log('表格区域:', tableSection);

    if (!container || !tableSection) {
        console.error('页面容器元素缺失');
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.innerHTML = '<div class="alert alert-danger">页面初始化失败：缺少必要的容器元素</div>';
        }
        return;
    }

    // 检查ProfessionalVoucherEditor类是否可用
    if (typeof ProfessionalVoucherEditor === 'undefined') {
        console.error('ProfessionalVoucherEditor类未定义，可能JavaScript文件加载失败');
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.innerHTML = '<div class="alert alert-danger">编辑器类未加载，请检查网络连接并刷新页面</div>';
        }
        return;
    }

    try {
        console.log('开始创建ProfessionalVoucherEditor实例...');

        // 初始化专业编辑器
        voucherEditor = new ProfessionalVoucherEditor({{ voucher.id }}, {
            autoSave: true,
            enableKeyboardNavigation: true,
            enableAutocomplete: true
        });

        console.log('专业凭证编辑器初始化完成');

    } catch (error) {
        console.error('编辑器初始化失败:', error);
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.innerHTML = '<div class="alert alert-danger">编辑器初始化失败: ' + error.message + '</div>';
        }
    }

    // 绑定基本信息保存事件
    ['voucherType', 'voucherDate', 'attachmentCount'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveBasicInfo);
        }
    });
    
    // F1 显示/隐藏快捷键帮助
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F1') {
            e.preventDefault();
            toggleShortcutHelp();
        }
    });
    
    // 默认显示快捷键帮助3秒
    setTimeout(() => {
        const help = document.getElementById('shortcut-help');
        if (help) {
            help.style.display = 'block';
            setTimeout(() => {
                help.style.display = 'none';
            }, 3000);
        }
    }, 1000);
});

function saveBasicInfo() {
    const data = {
        voucher_type: document.getElementById('voucherType').value,
        voucher_date: document.getElementById('voucherDate').value,
        attachment_count: parseInt(document.getElementById('attachmentCount').value) || 0
    };
    
    fetch(`/financial/vouchers/{{ voucher.id }}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            voucherEditor.showMessage('基本信息保存成功');
        } else {
            voucherEditor.showMessage(result.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('保存基本信息失败:', error);
        voucherEditor.showMessage('保存失败', 'error');
    });
}

function toggleShortcutHelp() {
    const help = document.getElementById('shortcut-help');
    if (help) {
        help.style.display = help.style.display === 'none' ? 'block' : 'none';
    }
}

// 页面离开前提醒保存
window.addEventListener('beforeunload', function(e) {
    if (voucherEditor && voucherEditor.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
        return e.returnValue;
    }
});
</script>
{% endblock %}
