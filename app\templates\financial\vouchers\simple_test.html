{% extends "financial/base.html" %}

{% block title %}简化凭证创建测试{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">简化凭证创建测试</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
                        <p>这是一个简化的凭证创建测试，用于诊断凭证创建失败的问题。</p>
                        <ul>
                            <li>用户：{{ current_user.username }} ({{ current_user.real_name }})</li>
                            <li>学校：{{ user_area.name }} (ID: {{ user_area.id }})</li>
                            <li>权限：财务凭证管理 - 创建</li>
                        </ul>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>测试凭证创建</h6>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary btn-lg btn-block" onclick="testCreateVoucher()">
                                        <i class="fas fa-play"></i> 开始测试
                                    </button>
                                    
                                    <div id="testResult" class="mt-3" style="display: none;">
                                        <!-- 测试结果将显示在这里 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>系统检查</h6>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-info btn-block" onclick="checkSystem()">
                                        <i class="fas fa-check"></i> 检查系统状态
                                    </button>
                                    
                                    <div id="systemStatus" class="mt-3" style="display: none;">
                                        <!-- 系统状态将显示在这里 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>常见问题解决方案</h6>
                                </div>
                                <div class="card-body">
                                    <div class="accordion" id="solutionsAccordion">
                                        <div class="card">
                                            <div class="card-header" id="headingOne">
                                                <h2 class="mb-0">
                                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                                        1. 表单验证失败
                                                    </button>
                                                </h2>
                                            </div>
                                            <div id="collapseOne" class="collapse" data-parent="#solutionsAccordion">
                                                <div class="card-body">
                                                    <ul>
                                                        <li>检查摘要字段是否为空</li>
                                                        <li>检查摘要长度是否超过200字符</li>
                                                        <li>检查凭证日期是否选择</li>
                                                        <li>检查凭证类型是否选择</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="card">
                                            <div class="card-header" id="headingTwo">
                                                <h2 class="mb-0">
                                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                                        2. 数据库约束错误
                                                    </button>
                                                </h2>
                                            </div>
                                            <div id="collapseTwo" class="collapse" data-parent="#solutionsAccordion">
                                                <div class="card-body">
                                                    <ul>
                                                        <li>凭证号重复：系统会自动生成唯一凭证号</li>
                                                        <li>外键约束：检查用户和学校关联是否正确</li>
                                                        <li>非空约束：检查必填字段是否完整</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="card">
                                            <div class="card-header" id="headingThree">
                                                <h2 class="mb-0">
                                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                                        3. 权限问题
                                                    </button>
                                                </h2>
                                            </div>
                                            <div id="collapseThree" class="collapse" data-parent="#solutionsAccordion">
                                                <div class="card-body">
                                                    <ul>
                                                        <li>检查用户是否有"财务凭证管理"权限</li>
                                                        <li>检查用户是否属于当前学校</li>
                                                        <li>检查用户角色配置是否正确</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 测试创建凭证
function testCreateVoucher() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    btn.disabled = true;
    
    fetch('{{ url_for("financial.simple_test_create") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('testResult');
        resultDiv.style.display = 'block';
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 测试成功！</h6>
                    <p><strong>凭证创建成功</strong></p>
                    <ul>
                        <li>凭证ID: ${data.voucher_id}</li>
                        <li>凭证号: ${data.voucher_number}</li>
                        <li>消息: ${data.message}</li>
                    </ul>
                    <a href="/financial/vouchers/${data.voucher_id}" class="btn btn-sm btn-primary" target="_blank">
                        <i class="fas fa-eye"></i> 查看凭证
                    </a>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 测试失败！</h6>
                    <p><strong>错误信息:</strong> ${data.message}</p>
                    ${data.traceback ? `
                        <details>
                            <summary>详细错误信息</summary>
                            <pre style="font-size: 12px; max-height: 200px; overflow-y: auto;">${data.traceback}</pre>
                        </details>
                    ` : ''}
                </div>
            `;
        }
    })
    .catch(error => {
        const resultDiv = document.getElementById('testResult');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误！</h6>
                <p>${error.message}</p>
            </div>
        `;
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 检查系统状态
function checkSystem() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';
    btn.disabled = true;
    
    fetch('{{ url_for("financial.check_system_status") }}')
    .then(response => response.json())
    .then(data => {
        const statusDiv = document.getElementById('systemStatus');
        statusDiv.style.display = 'block';
        
        const statusItems = [
            { label: '数据库连接', value: data.database_connection, key: 'database_connection' },
            { label: '用户权限', value: data.user_permissions, key: 'user_permissions' },
            { label: '区域访问', value: data.area_access, key: 'area_access' },
            { label: '凭证表访问', value: data.voucher_table, key: 'voucher_table' }
        ];
        
        let statusHtml = '<div class="table-responsive"><table class="table table-sm">';
        statusHtml += '<thead><tr><th>检查项</th><th>状态</th></tr></thead><tbody>';
        
        statusItems.forEach(item => {
            const statusIcon = item.value ? 
                '<i class="fas fa-check-circle text-success"></i>' : 
                '<i class="fas fa-times-circle text-danger"></i>';
            const statusText = item.value ? '正常' : '异常';
            
            statusHtml += `<tr><td>${item.label}</td><td>${statusIcon} ${statusText}</td></tr>`;
        });
        
        statusHtml += `<tr><td>现有凭证数量</td><td>${data.existing_vouchers}</td></tr>`;
        statusHtml += '</tbody></table></div>';
        
        if (data.error_message) {
            statusHtml += `<div class="alert alert-danger mt-2"><strong>错误:</strong> ${data.error_message}</div>`;
        }
        
        statusDiv.innerHTML = statusHtml;
    })
    .catch(error => {
        const statusDiv = document.getElementById('systemStatus');
        statusDiv.style.display = 'block';
        statusDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 检查失败！</h6>
                <p>${error.message}</p>
            </div>
        `;
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}
</script>
{% endblock %}
