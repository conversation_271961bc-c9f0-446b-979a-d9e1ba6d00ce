msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Fava\n"
"Language: fr\n"

#: frontend/src/reports/routes.ts:29
#: frontend/src/sidebar/AsideContents.svelte:67
msgid "Errors"
msgstr "Erreurs"

#: frontend/src/sidebar/FilterForm.svelte:62
msgid "Time"
msgstr "Période"

#: frontend/src/entry-forms/AccountInput.svelte:32
#: frontend/src/modals/DocumentUpload.svelte:68
#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:29
#: frontend/src/sidebar/FilterForm.svelte:73
#: src/fava/templates/statistics.html:26
msgid "Account"
msgstr "Compte"

#: frontend/src/entry-forms/Transaction.svelte:103
#: frontend/src/entry-forms/Transaction.svelte:106
#: src/fava/templates/_journal_table.html:50
msgid "Payee"
msgstr "Bénéficiaire"

#: frontend/src/reports/tree_reports/index.ts:14
msgid "Income Statement"
msgstr "Compte de résultat"

#: frontend/src/reports/tree_reports/index.ts:21
#: frontend/src/sidebar/AsideContents.svelte:27
msgid "Balance Sheet"
msgstr "Bilan"

#: frontend/src/reports/tree_reports/index.ts:28
#: frontend/src/sidebar/AsideContents.svelte:28
msgid "Trial Balance"
msgstr "Balance générale"

#: frontend/src/sidebar/AsideContents.svelte:29
#: src/fava/templates/journal.html:5
msgid "Journal"
msgstr "Journal"

#: frontend/src/reports/holdings/Holdings.svelte:56
#: frontend/src/reports/routes.ts:34
#: frontend/src/sidebar/AsideContents.svelte:30
msgid "Query"
msgstr "Requête"

#: frontend/src/reports/holdings/Holdings.svelte:18
#: frontend/src/reports/holdings/Holdings.svelte:20
#: frontend/src/reports/holdings/index.ts:91
#: frontend/src/sidebar/AsideContents.svelte:44
msgid "Holdings"
msgstr "Comptes"

#: frontend/src/reports/commodities/index.ts:35
#: frontend/src/sidebar/AsideContents.svelte:45
msgid "Commodities"
msgstr "Valeurs"

#: frontend/src/reports/events/Events.svelte:17
#: frontend/src/reports/events/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:49
msgid "Events"
msgstr "Événements"

#: frontend/src/reports/editor/index.ts:27
#: frontend/src/sidebar/AsideContents.svelte:56
msgid "Editor"
msgstr "Éditeur"

#: frontend/src/sidebar/AsideContents.svelte:74
#: src/fava/templates/options.html:3
msgid "Options"
msgstr "Options"

#: frontend/src/sidebar/AsideContents.svelte:53
#: src/fava/templates/statistics.html:6
msgid "Statistics"
msgstr "Statistiques"

#: frontend/src/sidebar/AsideContents.svelte:75 src/fava/templates/help.html:3
msgid "Help"
msgstr "Aide"

#: frontend/src/reports/commodities/CommodityTable.svelte:13
#: frontend/src/reports/documents/Table.svelte:23
#: frontend/src/reports/events/EventTable.svelte:10
#: src/fava/templates/_journal_table.html:48
msgid "Date"
msgstr "Date"

#: src/fava/templates/_journal_table.html:49
msgid "F"
msgstr "F"

#: frontend/src/reports/commodities/CommodityTable.svelte:14
#: src/fava/templates/_journal_table.html:53
msgid "Price"
msgstr "Prix"

#: src/fava/templates/_journal_table.html:52
msgid "Cost"
msgstr "Coût"

#: src/fava/templates/_journal_table.html:52
msgid "Change"
msgstr "Opération"

#: frontend/src/entry-forms/Balance.svelte:17
#: frontend/src/modals/AddEntry.svelte:15
#: src/fava/templates/_journal_table.html:53
#: src/fava/templates/statistics.html:30
msgid "Balance"
msgstr "Solde"

#: frontend/src/editor/SaveButton.svelte:8
#: frontend/src/modals/AddEntry.svelte:62
#: frontend/src/reports/import/Extract.svelte:94
msgid "Save"
msgstr "Sauvegarder"

#: frontend/src/editor/SaveButton.svelte:8
msgid "Saving..."
msgstr "Sauvegarde…"

#: frontend/src/main.ts:88
msgid "File change detected. Click to reload."
msgstr "Changement de fichier détecté. Cliquez pour recharger."

#: frontend/src/tree-table/AccountCellHeader.svelte:23
msgid "Expand all accounts"
msgstr "Développer tous les comptes"

#: frontend/src/tree-table/AccountCellHeader.svelte:28
msgid "Expand all"
msgstr "Développer tout"

#: frontend/src/reports/accounts/AccountReport.svelte:45
msgid "Account Journal"
msgstr "Journal du compte"

#: frontend/src/reports/accounts/AccountReport.svelte:51
#: frontend/src/reports/accounts/AccountReport.svelte:54
#: src/fava/json_api.py:632
msgid "Changes"
msgstr "Opérations"

#: frontend/src/reports/accounts/AccountReport.svelte:60
#: frontend/src/reports/accounts/AccountReport.svelte:63
msgid "Balances"
msgstr "Balances"

#: frontend/src/reports/errors/Errors.svelte:58
msgid "Show source %(file)s:%(lineno)s"
msgstr "Afficher la source %(file)s:%(lineno)s"

#: frontend/src/reports/editor/EditorMenu.svelte:40
#: frontend/src/reports/errors/Errors.svelte:32
msgid "File"
msgstr "Fichier"

#: frontend/src/reports/errors/Errors.svelte:33
#: frontend/src/reports/import/Extract.svelte:100
msgid "Line"
msgstr "Ligne"

#: frontend/src/reports/errors/Errors.svelte:34
msgid "Error"
msgstr "Erreur"

#: frontend/src/reports/errors/Errors.svelte:78
msgid "No errors."
msgstr "Aucune erreur."

#: frontend/src/reports/events/Events.svelte:32
msgid "Event: %(type)s"
msgstr "Événement :%(type)s"

#: frontend/src/reports/events/EventTable.svelte:11
msgid "Description"
msgstr "Description"

#: src/fava/templates/help.html:8
msgid "Help pages"
msgstr "Pages d’aide"

#: frontend/src/entry-forms/Balance.svelte:32
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:39
msgid "Currency"
msgstr "Devise"

#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:49
msgid "Cost currency"
msgstr "Devise pour les coûts"

#: frontend/src/reports/holdings/Holdings.svelte:25
#: frontend/src/reports/holdings/Holdings.svelte:28
#: frontend/src/reports/holdings/Holdings.svelte:35
#: frontend/src/reports/holdings/Holdings.svelte:38
#: frontend/src/reports/holdings/Holdings.svelte:45
#: frontend/src/reports/holdings/Holdings.svelte:48
msgid "Holdings by"
msgstr "Comptes par"

#: frontend/src/stores/accounts.ts:22 src/fava/json_api.py:514
#: src/fava/json_api.py:534
msgid "Net Profit"
msgstr "Bénéfice net"

#: src/fava/json_api.py:520
msgid "Income"
msgstr "Revenus"

#: src/fava/json_api.py:526
msgid "Expenses"
msgstr "Dépenses"

#: frontend/src/entry-forms/EntryMetadata.svelte:57
#: src/fava/templates/options.html:10 src/fava/templates/options.html:27
msgid "Key"
msgstr "Clé"

#: frontend/src/entry-forms/EntryMetadata.svelte:67
#: src/fava/templates/options.html:11 src/fava/templates/options.html:28
msgid "Value"
msgstr "Valeur"

#: frontend/src/reports/query/QueryLinks.svelte:25
msgid "Download as"
msgstr "Télécharger en tant que"

#: src/fava/templates/statistics.html:12
msgid "Postings per Account"
msgstr "Opérations par compte"

#: src/fava/templates/statistics.html:80
msgid "Total"
msgstr "Total"

#: src/fava/templates/statistics.html:20
msgid "Update Activity"
msgstr "Activité"

#: src/fava/templates/statistics.html:21
msgid "Click to copy balance directives for accounts (except green ones) to the clipboard."
msgstr "Cliquez pour copier les directives balance des comptes (sauf pour ceux en verts) dans le presse-papiers."

#: src/fava/templates/statistics.html:22
msgid "Copy balance directives"
msgstr "Copier les directives balance"

#: src/fava/templates/statistics.html:29
msgid "Last Entry"
msgstr "Dernière entrée"

#: src/fava/templates/statistics.html:62
msgid "Entries per Type"
msgstr "Entrées par type"

#: src/fava/templates/statistics.html:66
msgid "Type"
msgstr "Type"

#: src/fava/templates/statistics.html:67
msgid "# Entries"
msgstr "# Entrées"

#: frontend/src/entry-forms/Transaction.svelte:113
#: frontend/src/entry-forms/Transaction.svelte:117
#: src/fava/templates/_journal_table.html:50
msgid "Narration"
msgstr "Description"

#: frontend/src/entry-forms/Balance.svelte:26
msgid "Number"
msgstr "Nombre"

#: frontend/src/reports/import/Extract.svelte:43
#: frontend/src/reports/import/index.ts:67
msgid "Import"
msgstr "Importer"

#: frontend/src/journal/JournalFilters.svelte:41
msgid "Budget entries"
msgstr "Entrées du budget"

#: frontend/src/reports/import/Extract.svelte:99
msgid "Source"
msgstr "Source"

#: frontend/src/reports/import/FileList.svelte:53
msgid "Extract"
msgstr "Extraire"

#: frontend/src/reports/query/QueryEditor.svelte:15
msgid "...enter a BQL query. 'help' to list available commands."
msgstr "… entrez une requête BQL. Utilisez ‘help’ pour voir les commandes disponibles."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Metadata"
msgstr "Métadonnées"

#: frontend/src/entry-forms/AddMetadataButton.svelte:18
#: frontend/src/entry-forms/EntryMetadata.svelte:78
msgid "Add metadata"
msgstr "Ajouter une métadonnée"

#: frontend/src/entry-forms/Note.svelte:15
#: frontend/src/modals/AddEntry.svelte:16
msgid "Note"
msgstr "Note"

#: frontend/src/modals/EntryContext.svelte:13
msgid "Location"
msgstr "Lieu"

#: frontend/src/modals/EntryContext.svelte:29
msgid "Context"
msgstr "Contexte"

#: frontend/src/modals/EntryContext.svelte:35
msgid "Balances before entry"
msgstr "Balances avant l’entrée"

#: frontend/src/modals/EntryContext.svelte:55
msgid "Balances after entry"
msgstr "Balances après l’entrée"

#: frontend/src/journal/JournalFilters.svelte:24
msgid "Cleared transactions"
msgstr "Transactions autorisées"

#: frontend/src/journal/JournalFilters.svelte:25
msgid "Pending transactions"
msgstr "Transactions en cours"

#: frontend/src/journal/JournalFilters.svelte:26
msgid "Other transactions"
msgstr "Autres transactions"

#: frontend/src/journal/JournalFilters.svelte:33
msgid "Documents with a #discovered tag"
msgstr "Documents avec une étiquette #discovered"

#: frontend/src/entry-forms/Transaction.svelte:126
#: frontend/src/journal/JournalFilters.svelte:43
msgid "Postings"
msgstr "Affectations"

#: frontend/src/modals/DocumentUpload.svelte:49
msgid "Upload file(s)"
msgstr "Téléverser des fichiers"

#: frontend/src/modals/DocumentUpload.svelte:72
#: frontend/src/reports/import/Import.svelte:197
msgid "Upload"
msgstr "Téléverser"

#: frontend/src/reports/editor/EditorMenu.svelte:54
msgid "Align Amounts"
msgstr "Aligner les montants"

#: frontend/src/reports/editor/EditorMenu.svelte:58
msgid "Toggle Comment (selection)"
msgstr "Commentaires (sélection)"

#: frontend/src/reports/editor/EditorMenu.svelte:62
msgid "Open all folds"
msgstr "Tout développer"

#: frontend/src/reports/editor/EditorMenu.svelte:66
msgid "Close all folds"
msgstr "Tout réduire"

#: src/fava/templates/options.html:6
msgid "Fava options"
msgstr "Options de Fava"

#: src/fava/templates/options.html:6
msgid "help"
msgstr "Aide"

#: src/fava/templates/options.html:23
msgid "Beancount options"
msgstr "Options de Beancount"

#: frontend/src/reports/query/QueryEditor.svelte:27
msgid "Submit"
msgstr "Envoyer"

#: frontend/src/tree-table/AccountCellHeader.svelte:12
msgid "Hold Shift while clicking to expand all children.\n"
"Hold Ctrl or Cmd while clicking to expand one level."
msgstr "Maintenez Shift en cliquant pour développer tous les nœuds.\n"
"Maintenez Ctrl or Cmd en cliquant pour développer un seul nœud."

#: frontend/src/modals/Export.svelte:14
msgid "Export"
msgstr "Exporter"

#: frontend/src/sidebar/FilterForm.svelte:84
msgid "Filter by tag, payee, ..."
msgstr "Filtrer par étiquette, bénéficiaire, …"

#: frontend/src/modals/Export.svelte:16
msgid "Download currently filtered entries as a Beancount file"
msgstr "Télécharger ces entrées filtrées comme fichier Beancount"

#: frontend/src/lib/interval.ts:22 src/fava/util/date.py:102
msgid "Yearly"
msgstr "Annuel"

#: frontend/src/lib/interval.ts:23 src/fava/util/date.py:103
msgid "Quarterly"
msgstr "Trimestriel"

#: frontend/src/lib/interval.ts:24 src/fava/util/date.py:104
msgid "Monthly"
msgstr "Mensuel"

#: frontend/src/lib/interval.ts:25 src/fava/util/date.py:105
msgid "Weekly"
msgstr "Hebdomadaire"

#: frontend/src/lib/interval.ts:26 src/fava/util/date.py:106
msgid "Daily"
msgstr "Journalier"

#: frontend/src/reports/editor/EditorMenu.svelte:52
msgid "Edit"
msgstr "Éditer"

#: frontend/src/entry-forms/Posting.svelte:77
msgid "Amount"
msgstr "Montant"

#: frontend/src/journal/JournalFilters.svelte:37
msgid "Documents with a #linked tag"
msgstr "Documents avec une étiquette #linked"

#: frontend/src/charts/ConversionAndInterval.svelte:12
msgid "At Cost"
msgstr "Coût"

#: frontend/src/charts/ConversionAndInterval.svelte:14
msgid "At Market Value"
msgstr "Valeur de marché"

#: frontend/src/charts/ConversionAndInterval.svelte:16
#: src/fava/templates/_journal_table.html:51
msgid "Units"
msgstr "Unités"

#: frontend/src/stores/chart.ts:30
msgid "Treemap"
msgstr "Treemap"

#: frontend/src/stores/chart.ts:31
msgid "Sunburst"
msgstr "Anneaux"

#: frontend/src/entry-forms/AccountInput.svelte:20
msgid "Should be one of the declared accounts"
msgstr "Doit être l’un des comptes déclarés"

#: frontend/src/charts/ChartSwitcher.svelte:21
#: frontend/src/reports/import/Extract.svelte:79
msgid "Previous"
msgstr "Précédent"

#: frontend/src/charts/ChartSwitcher.svelte:28
#: frontend/src/reports/import/Extract.svelte:84
msgid "Next"
msgstr "Suivant"

#: frontend/src/modals/AddEntry.svelte:14
msgid "Transaction"
msgstr "Transaction"

#: frontend/src/modals/AddEntry.svelte:40
msgid "Add"
msgstr "Ajouter"

#: frontend/src/reports/documents/Documents.svelte:68
msgid "Move or rename document"
msgstr "Déplacer ou renommer le document"

#: frontend/src/reports/documents/Table.svelte:24
msgid "Name"
msgstr "Nom"

#: frontend/src/reports/documents/index.ts:16
#: frontend/src/sidebar/AsideContents.svelte:46
msgid "Documents"
msgstr "Documents"

#: frontend/src/editor/DeleteButton.svelte:7
#: frontend/src/editor/DeleteButton.svelte:10
#: frontend/src/reports/import/FileList.svelte:29
msgid "Delete"
msgstr "Supprimer"

#: frontend/src/stores/chart.ts:44
msgid "Line chart"
msgstr "Graphique en courbe"

#: frontend/src/stores/chart.ts:45
msgid "Area chart"
msgstr "Graphique en aire"

#: frontend/src/reports/import/FileList.svelte:52
msgid "Continue"
msgstr "Continuer"

#: frontend/src/reports/import/FileList.svelte:63
msgid "Clear"
msgstr "Effacer"

#: frontend/src/sidebar/AccountSelector.svelte:22
msgid "Go to account"
msgstr "Aller au compte"

#: frontend/src/reports/import/Import.svelte:78
msgid "Delete this file?"
msgstr "Supprimer ce fichier ?"

#: frontend/src/reports/import/Import.svelte:164
msgid "No files were found for import."
msgstr "Aucun fichier à importer n'a été trouvé."

#: frontend/src/journal/JournalFilters.svelte:7
msgid "Toggle %(type)s entries"
msgstr "Basculer %(type)s entrées"

#: frontend/src/charts/ConversionAndInterval.svelte:18
msgid "Converted to %(currency)s"
msgstr "Converti en %(currency)s"

#: frontend/src/stores/chart.ts:58
msgid "Stacked Bars"
msgstr "Barres empilées"

#: frontend/src/stores/chart.ts:59
msgid "Single Bars"
msgstr "Barres simples"

#: frontend/src/charts/HierarchyContainer.svelte:32
msgid "Chart is empty."
msgstr "Le graphique est vide."

#: frontend/src/journal/JournalFilters.svelte:42
msgid "Toggle metadata"
msgstr "Basculer les métadonnées"

#: frontend/src/journal/JournalFilters.svelte:43
msgid "Toggle postings"
msgstr "Basculer les opérations"

#: src/fava/internal_api.py:221
msgid "Net Worth"
msgstr "Valeur nette"

#: src/fava/internal_api.py:170
msgid "Account Balance"
msgstr "Solde du compte"

#: frontend/src/editor/SliceEditor.svelte:91
msgid "reload"
msgstr "Recharger"

#: frontend/src/modals/AddEntry.svelte:60
msgid "continue"
msgstr "Continuer"

#: frontend/src/editor/DeleteButton.svelte:7
msgid "Deleting..."
msgstr "Suppression..."

#: frontend/src/sidebar/AsideContents.svelte:60
msgid "Add Journal Entry"
msgstr ""

