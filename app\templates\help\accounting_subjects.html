{% extends "base.html" %}

{% block title %}会计科目管理帮助{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('help.index') }}">帮助中心</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('help.financial_help') }}">财务管理</a></li>
            <li class="breadcrumb-item active">会计科目管理</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h1><i class="fas fa-chart-line"></i> 会计科目管理帮助</h1>
                    <p class="lead">完整的会计科目管理指南和故障排除</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5><i class="fas fa-rocket"></i> 快速操作</h5>
                    <div class="mt-3">
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-outline-light me-2">
                            <i class="fas fa-list-alt"></i> 会计科目列表
                        </a>
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-outline-light me-2">
                            <i class="fas fa-tools"></i> 会计科目管理
                        </a>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-outline-light">
                            <i class="fas fa-file-invoice"></i> 测试凭证功能
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 使用指南 -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-graduation-cap"></i> 使用指南</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-route"></i> 操作步骤</h6>
                            <ol>
                                <li><strong>检查状态</strong> - 查看当前会计科目情况</li>
                                <li><strong>选择方案</strong> - 根据提示选择合适的操作</li>
                                <li><strong>执行操作</strong> - 点击相应按钮执行</li>
                                <li><strong>验证结果</strong> - 测试API和凭证功能</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle"></i> 科目类型</h6>
                            <div class="mb-2">
                                <span class="badge bg-primary me-2">系统科目</span>
                                标准化、全局共享、不可修改
                            </div>
                            <div class="mb-2">
                                <span class="badge bg-warning text-dark me-2">学校科目</span>
                                学校专属、可自定义、基于系统科目
                            </div>
                            <div class="alert alert-success mt-3 mb-0">
                                <small><i class="fas fa-thumbs-up"></i> <strong>推荐：</strong>优先使用系统科目，特殊需求时再创建学校科目</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 常见问题 -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-question-circle"></i> 常见问题与解决方案</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    凭证编辑页面的会计科目下拉框为空怎么办？
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <div class="alert alert-info">
                                        <strong>原因：</strong>系统中没有可用的会计科目数据
                                    </div>
                                    <strong>解决步骤：</strong>
                                    <ol>
                                        <li>打开<a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
                                        <li>点击"检查状态"查看当前情况</li>
                                        <li>如果系统科目数量为0，点击"初始化系统科目"或"修复数据结构"</li>
                                        <li>等待操作完成，确认系统科目数量大于0</li>
                                        <li>点击"测试API"验证科目数据正常</li>
                                        <li>回到凭证编辑页面重新测试</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    <i class="fas fa-cog text-primary me-2"></i>
                                    系统科目数量为0，但有学校科目怎么办？
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <div class="alert alert-danger">
                                        <strong>问题：</strong>这是数据结构错误，需要修复
                                    </div>
                                    <strong>解决方案：</strong>
                                    <ol>
                                        <li>打开<a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
                                        <li>点击红色的"修复数据结构"按钮</li>
                                        <li>确认操作（会重置所有会计科目数据）</li>
                                        <li>等待修复完成</li>
                                        <li>验证系统科目数量变为36个</li>
                                    </ol>
                                    <div class="alert alert-warning mt-2">
                                        <small><i class="fas fa-exclamation-triangle"></i> 注意：此操作会删除现有科目数据，请确保没有重要凭证依赖</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    <i class="fas fa-plus text-success me-2"></i>
                                    如何添加自定义的会计科目？
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <strong>方法一：复制系统科目后修改</strong>
                                    <ol>
                                        <li>确保系统科目已初始化</li>
                                        <li>在管理器中点击"从系统复制"按钮</li>
                                        <li>访问<a href="{{ url_for('financial.accounting_subjects_index') }}" target="_blank">会计科目管理页面</a></li>
                                        <li>编辑或添加自定义科目</li>
                                    </ol>
                                    <strong>方法二：直接创建</strong>
                                    <ol>
                                        <li>访问<a href="{{ url_for('financial.create_accounting_subject') }}" target="_blank">创建会计科目页面</a></li>
                                        <li>填写科目信息并保存</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 快速链接 -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h6><i class="fas fa-link"></i> 快速链接</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list-alt"></i> 会计科目列表
                        </a>
                        <a href="{{ url_for('financial.create_accounting_subject') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus"></i> 新增科目
                        </a>
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-tools"></i> 会计科目管理
                        </a>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-file-invoice"></i> 财务凭证
                        </a>
                    </div>
                </div>
            </div>

            <!-- 相关帮助 -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-book"></i> 相关帮助</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{{ url_for('help.financial_help') }}" class="text-decoration-none">
                                <i class="fas fa-calculator text-primary"></i> 财务管理总览
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('help.troubleshooting') }}" class="text-decoration-none">
                                <i class="fas fa-exclamation-triangle text-warning"></i> 故障排除
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('help.index') }}" class="text-decoration-none">
                                <i class="fas fa-home text-success"></i> 帮助中心首页
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
